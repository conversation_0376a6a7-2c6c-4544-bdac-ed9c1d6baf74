#æ°æ®åºé¾æ¥éç½®
db.driver.name=com.mysql.cj.jdbc.Driver
db.url=**********************************************************************************************************************************
db.username=root
db.password=ly@LYmysql
#æä»¶è¾åºè·¯å¾
path.base=D:/Workspace/java_code/bilibili-backend/generate
#åå
package.base=com.bilibili
#æä¸¾å
package.enums=entity.enums
#po beanå
package.bean=entity.po
#queryå¯¹è±¡å
package.param=entity.query
#voå
package.vo=entity.vo
#mapperå
package.mapper=mappers
#serviceå
package.service=service
#serviceå®ç°å
package.service.impl=service.impl
#controllerå
package.controller=controller
#åç¼å®ä¹
#mapperåç¼å®ä¹
suffix.mapper=Mapper
#mapper.xmlåç¼å®ä¹
suffix.mapper.xml=Mapper
#Serviceåç¼å®ä¹
suffix.service=Service
#Serviceå®ç°åç¼å®ä¹
suffix.service.impl=ServiceImpl
#Controlleråç¼å®ä¹
suffix.controller=Controller
#æ¨¡ç³æç´¢åç¼å®ä¹
suffix.property.fuzzy=Fuzzy
#æç´¢å¯¹è±¡åç¼å®ä¹
suffix.bean.param=Query
#æ¶é´åºé´æç´¢ å¼å§åç¼å®ä¹
suffix.bean.param.time.start=Start
#æ¶é´åºé´æç´¢ ç»æåç¼å®ä¹
suffix.bean.param.time.end=End
#æ¯å¦å¿½ç¥è¡¨åç¼
ignore.table.prefix=false
#åè¡¨åç¼å®ä¹
table.split.prefix=template
#jsonè¿åå¿½ç¥çå±æ§
ignore.bean.tojson.column=company_id,phone
ignore.bean.tojson.expression=@JsonIgnore
ignore.bean.tojson.class=com.fasterxml.jackson.annotation.JsonIgnore
#æ¥ææ ¼å¼å
bean.date.expression=@JsonFormat(pattern = "%s", timezone = "GMT+8")
bean.date.expression.class=com.fasterxml.jackson.annotation.JsonFormat
#æ¥æåæ°æ ¼å¼å
bean.data.format=@DateTimeFormat(pattern = "%s")
bean.date.format.class=org.springframework.format.annotation.DateTimeFormat