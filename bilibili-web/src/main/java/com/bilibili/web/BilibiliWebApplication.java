package com.bilibili.web;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication(scanBasePackages = "com.bilibili")
@MapperScan(basePackages = "com.bilibili.common.mapper") // 批量扫描 mapper 接口，接口无需再添加 @Mapper注解
public class BilibiliWebApplication {

    public static void main(String[] args) {
        SpringApplication.run(BilibiliWebApplication.class, args);
    }

}
