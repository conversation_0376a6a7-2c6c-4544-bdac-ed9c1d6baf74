package com.bilibili.web.controller;


import com.bilibili.common.model.dto.web.account.LoginRequest;
import com.bilibili.common.model.dto.web.account.RegisterRequest;
import com.bilibili.common.model.vo.UserVO;
import com.bilibili.common.service.UserService;
import com.bilibili.common.utils.Response;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;


/**
 * 用户
 */
@RestController
@RequestMapping("/account")
public class AccountController {

	@Resource
	private UserService userService;


	/**
	 * 获取图形验证码
	 * @return
	 */
	@RequestMapping("/checkCode")
	public Response<Map<String, String>> checkCode() {
		return Response.success(userService.generateCaptcha());
	}


	/**
	 * 注册
	 * @param registerRequest 注册请求
	 * @return 注册结果
	 */
	@RequestMapping("/register")
	public Response<Boolean> register(@RequestBody @Validated RegisterRequest registerRequest) {
		String email = registerRequest.getEmail();
		String nickName = registerRequest.getNickName();
		String registerPassword = registerRequest.getRegisterPassword();
		String reRegisterPassword = registerRequest.getReRegisterPassword();
		String checkCode = registerRequest.getCheckCode();
		String checkCodeKey = registerRequest.getCheckCodeKey();
		Boolean result = userService.register(email, nickName, registerPassword, reRegisterPassword, checkCode, checkCodeKey);
		return Response.success(result);
	}

	/**
	 * 登录
	 * @param loginRequest 登录请求
	 * @return 用户信息
	 */
	@RequestMapping("/login")
	public Response<UserVO> login(@RequestBody @Validated LoginRequest loginRequest) {
		String email = loginRequest.getEmail();
		String password = loginRequest.getPassword();
		String checkCode = loginRequest.getCheckCode();
		String checkCodeKey = loginRequest.getCheckCodeKey();
		UserVO userVO = userService.login(email, password, checkCode, checkCodeKey);
		return Response.success(userVO);
	}

	/**
	 * 退出登录
	 * @return 退出结果
	 */
	@RequestMapping("/logout")
	public Response<Void> logout() {
		userService.logout();
		return Response.success();
	}

}