2025-07-23 11:04:57 [INFO][org.hibernate.validator.internal.util.Version][<clinit>][21]-> HV000001: Hibernate Validator 8.0.2.Final
2025-07-23 11:04:57 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStarting][53]-> Starting BilibiliAdminApplication using Java 21.0.7 with PID 17512 (D:\Workspace\java_code\bilibili-backend\bilibili-admin\target\classes started by 26788 in D:\Workspace\java_code\bilibili-backend)
2025-07-23 11:04:57 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStartupProfileInfo][652]-> No active profile set, falling back to 1 default profile: "default"
2025-07-23 11:04:58 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-23 11:04:58 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-23 11:04:58 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 14 ms. Found 0 Elasticsearch repository interfaces.
2025-07-23 11:04:58 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-23 11:04:58 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-23 11:04:58 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 1 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-23 11:04:58 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-23 11:04:58 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-23 11:04:58 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 1 ms. Found 0 Redis repository interfaces.
2025-07-23 11:04:58 [INFO][org.springframework.boot.web.embedded.tomcat.TomcatWebServer][initialize][111]-> Tomcat initialized with port 7070 (http)
2025-07-23 11:04:59 [INFO][org.apache.coyote.http11.Http11NioProtocol][log][173]-> Initializing ProtocolHandler ["http-nio-7070"]
2025-07-23 11:04:59 [INFO][org.apache.catalina.core.StandardService][log][173]-> Starting service [Tomcat]
2025-07-23 11:04:59 [INFO][org.apache.catalina.core.StandardEngine][log][173]-> Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-07-23 11:04:59 [INFO][org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/admin]][log][173]-> Initializing Spring embedded WebApplicationContext
2025-07-23 11:04:59 [INFO][org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext][prepareWebApplicationContext][301]-> Root WebApplicationContext: initialization completed in 1654 ms
2025-07-23 11:05:00 [INFO][org.apache.coyote.http11.Http11NioProtocol][log][173]-> Starting ProtocolHandler ["http-nio-7070"]
2025-07-23 11:05:00 [INFO][org.springframework.boot.web.embedded.tomcat.TomcatWebServer][start][243]-> Tomcat started on port 7070 (http) with context path '/admin'
2025-07-23 11:05:00 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStarted][59]-> Started BilibiliAdminApplication in 4.03 seconds (process running for 5.621)
2025-07-23 13:24:49 [INFO][org.springframework.boot.web.embedded.tomcat.GracefulShutdown][shutDownGracefully][54]-> Commencing graceful shutdown. Waiting for active requests to complete
2025-07-23 13:24:49 [INFO][org.springframework.boot.web.embedded.tomcat.GracefulShutdown][doShutdown][76]-> Graceful shutdown complete
2025-07-23 17:14:14 [INFO][org.hibernate.validator.internal.util.Version][<clinit>][21]-> HV000001: Hibernate Validator 8.0.2.Final
2025-07-23 17:14:14 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStarting][53]-> Starting BilibiliAdminApplication using Java 21.0.7 with PID 15016 (D:\Workspace\java_code\bilibili-backend\bilibili-admin\target\classes started by 26788 in D:\Workspace\java_code\bilibili-backend)
2025-07-23 17:14:14 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStartupProfileInfo][652]-> No active profile set, falling back to 1 default profile: "default"
2025-07-23 17:14:15 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-23 17:14:15 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-23 17:14:15 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 4 ms. Found 0 Elasticsearch repository interfaces.
2025-07-23 17:14:15 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-23 17:14:15 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-23 17:14:15 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 0 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-23 17:14:15 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-23 17:14:15 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-23 17:14:15 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 0 ms. Found 0 Redis repository interfaces.
2025-07-23 17:14:15 [WARN][org.mybatis.spring.mapper.ClassPathMapperScanner][warn][44]-> No MyBatis mapper was found in '[com.bilibili.admin]' package. Please check your configuration.
2025-07-23 17:14:15 [INFO][org.springframework.boot.web.embedded.tomcat.TomcatWebServer][initialize][111]-> Tomcat initialized with port 7070 (http)
2025-07-23 17:14:15 [INFO][org.apache.coyote.http11.Http11NioProtocol][log][173]-> Initializing ProtocolHandler ["http-nio-7070"]
2025-07-23 17:14:15 [INFO][org.apache.catalina.core.StandardService][log][173]-> Starting service [Tomcat]
2025-07-23 17:14:15 [INFO][org.apache.catalina.core.StandardEngine][log][173]-> Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-07-23 17:14:15 [INFO][org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/admin]][log][173]-> Initializing Spring embedded WebApplicationContext
2025-07-23 17:14:15 [INFO][org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext][prepareWebApplicationContext][301]-> Root WebApplicationContext: initialization completed in 1098 ms
2025-07-23 17:14:15 [INFO][com.alibaba.druid.spring.boot3.autoconfigure.DruidDataSourceAutoConfigure][dataSource][66]-> Init DruidDataSource
2025-07-23 17:14:16 [INFO][com.alibaba.druid.pool.DruidDataSource][init][1007]-> {dataSource-1} inited
2025-07-23 17:14:16 [INFO][org.apache.coyote.http11.Http11NioProtocol][log][173]-> Starting ProtocolHandler ["http-nio-7070"]
2025-07-23 17:14:16 [INFO][org.springframework.boot.web.embedded.tomcat.TomcatWebServer][start][243]-> Tomcat started on port 7070 (http) with context path '/admin'
2025-07-23 17:14:16 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStarted][59]-> Started BilibiliAdminApplication in 2.95 seconds (process running for 3.594)
2025-07-23 18:18:49 [INFO][org.springframework.boot.web.embedded.tomcat.GracefulShutdown][shutDownGracefully][54]-> Commencing graceful shutdown. Waiting for active requests to complete
2025-07-23 18:18:49 [INFO][org.springframework.boot.web.embedded.tomcat.GracefulShutdown][doShutdown][76]-> Graceful shutdown complete
2025-07-23 18:18:49 [INFO][com.alibaba.druid.pool.DruidDataSource][close][2212]-> {dataSource-1} closing ...
2025-07-23 18:18:49 [INFO][com.alibaba.druid.pool.DruidDataSource][close][2285]-> {dataSource-1} closed
