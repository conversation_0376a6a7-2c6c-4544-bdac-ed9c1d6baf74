2025-07-30 20:04:46 [INFO][org.hibernate.validator.internal.util.Version][<clinit>][21]-> HV000001: Hibernate Validator 8.0.2.Final
2025-07-30 20:04:46 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStarting][53]-> Starting BilibiliAdminApplication using Java 21.0.7 with PID 7564 (D:\Workspace\java_code\bilibili-backend\bilibili-admin\target\classes started by 26788 in D:\Workspace\java_code\bilibili-backend)
2025-07-30 20:04:46 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStartupProfileInfo][652]-> No active profile set, falling back to 1 default profile: "default"
2025-07-30 20:04:47 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 20:04:47 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-30 20:04:47 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 11 ms. Found 0 Elasticsearch repository interfaces.
2025-07-30 20:04:47 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 20:04:47 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-30 20:04:47 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 0 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-30 20:04:47 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 20:04:47 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 20:04:47 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 0 ms. Found 0 Redis repository interfaces.
2025-07-30 20:04:47 [INFO][org.springframework.boot.web.embedded.tomcat.TomcatWebServer][initialize][111]-> Tomcat initialized with port 7070 (http)
2025-07-30 20:04:47 [INFO][org.apache.coyote.http11.Http11NioProtocol][log][173]-> Initializing ProtocolHandler ["http-nio-7070"]
2025-07-30 20:04:47 [INFO][org.apache.catalina.core.StandardService][log][173]-> Starting service [Tomcat]
2025-07-30 20:04:47 [INFO][org.apache.catalina.core.StandardEngine][log][173]-> Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-07-30 20:04:47 [INFO][org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/admin]][log][173]-> Initializing Spring embedded WebApplicationContext
2025-07-30 20:04:47 [INFO][org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext][prepareWebApplicationContext][301]-> Root WebApplicationContext: initialization completed in 1468 ms
2025-07-30 20:04:47 [INFO][com.alibaba.druid.spring.boot3.autoconfigure.DruidDataSourceAutoConfigure][dataSource][66]-> Init DruidDataSource
2025-07-30 20:04:48 [INFO][com.alibaba.druid.pool.DruidDataSource][init][1007]-> {dataSource-1} inited
2025-07-30 20:04:49 [INFO][org.apache.coyote.http11.Http11NioProtocol][log][173]-> Starting ProtocolHandler ["http-nio-7070"]
2025-07-30 20:04:49 [INFO][org.springframework.boot.web.embedded.tomcat.TomcatWebServer][start][243]-> Tomcat started on port 7070 (http) with context path '/admin'
2025-07-30 20:04:49 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStarted][59]-> Started BilibiliAdminApplication in 3.798 seconds (process running for 5.619)
2025-07-30 20:05:02 [INFO][org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/admin]][log][173]-> Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 20:05:02 [INFO][org.springframework.web.servlet.DispatcherServlet][initServletBean][532]-> Initializing Servlet 'dispatcherServlet'
2025-07-30 20:05:02 [INFO][org.springframework.web.servlet.DispatcherServlet][initServletBean][554]-> Completed initialization in 7 ms
2025-07-30 20:05:02 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 20:06:31 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 20:06:42 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 20:06:43 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 20:06:43 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 20:06:43 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/index/getWeekStatisticsInfo, 错误信息:
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource index/getWeekStatisticsInfo.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-30 20:06:43 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/index/getActualTimeStatisticsInfo, 错误信息:
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource index/getActualTimeStatisticsInfo.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-30 20:07:04 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 20:07:48 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 20:07:48 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 20:07:52 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/videoInfo/loadVideoList, 错误信息:
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource videoInfo/loadVideoList.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-30 20:08:01 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 20:08:07 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/interact/loadComment, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 20:08:07 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 20:08:28 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 20:08:35 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 20:08:36 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 20:08:36 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 20:08:36 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/index/getWeekStatisticsInfo, 错误信息:
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource index/getWeekStatisticsInfo.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-30 20:08:36 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/index/getActualTimeStatisticsInfo, 错误信息:
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource index/getActualTimeStatisticsInfo.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-30 20:08:41 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 20:08:54 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 20:08:54 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/interact/loadComment, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 20:08:54 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 20:17:32 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 20:17:34 [ERROR][com.bilibili.common.utils.RedisUtil][setex][78]-> 设置redisKey:bilibili:captcha:c6cdf741-739a-4a0b-bb80-013d27de9ded,value:5失败
2025-07-30 20:17:54 [INFO][io.lettuce.core.protocol.ConnectionWatchdog][log][171]-> Reconnecting, last destination was /8.152.202.56:6379
2025-07-30 20:17:54 [INFO][io.lettuce.core.protocol.ReconnectionHandler][lambda$null$3][178]-> Reconnected to 8.152.202.56/<unresolved>:6379
2025-07-30 20:18:47 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 20:18:55 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 20:18:55 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 20:18:55 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 20:18:55 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/index/getActualTimeStatisticsInfo, 错误信息:
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource index/getActualTimeStatisticsInfo.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-30 20:18:55 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/index/getWeekStatisticsInfo, 错误信息:
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource index/getWeekStatisticsInfo.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-30 20:19:05 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 20:19:05 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 20:19:05 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/index/getActualTimeStatisticsInfo, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 20:19:05 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/index/getWeekStatisticsInfo, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 20:19:05 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:51:32 [INFO][org.springframework.boot.web.embedded.tomcat.GracefulShutdown][shutDownGracefully][54]-> Commencing graceful shutdown. Waiting for active requests to complete
2025-07-30 21:51:32 [INFO][org.springframework.boot.web.embedded.tomcat.GracefulShutdown][doShutdown][76]-> Graceful shutdown complete
2025-07-30 21:51:32 [INFO][com.alibaba.druid.pool.DruidDataSource][close][2212]-> {dataSource-1} closing ...
2025-07-30 21:51:32 [INFO][com.alibaba.druid.pool.DruidDataSource][close][2285]-> {dataSource-1} closed
2025-07-30 21:51:39 [INFO][org.hibernate.validator.internal.util.Version][<clinit>][21]-> HV000001: Hibernate Validator 8.0.2.Final
2025-07-30 21:51:39 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStarting][53]-> Starting BilibiliAdminApplication using Java 21.0.7 with PID 23188 (D:\Workspace\java_code\bilibili-backend\bilibili-admin\target\classes started by 26788 in D:\Workspace\java_code\bilibili-backend)
2025-07-30 21:51:39 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStartupProfileInfo][652]-> No active profile set, falling back to 1 default profile: "default"
2025-07-30 21:51:40 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 21:51:40 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-30 21:51:40 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 10 ms. Found 0 Elasticsearch repository interfaces.
2025-07-30 21:51:40 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 21:51:40 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-30 21:51:40 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 0 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-30 21:51:40 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 21:51:40 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 21:51:40 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 0 ms. Found 0 Redis repository interfaces.
2025-07-30 21:51:41 [INFO][org.springframework.boot.web.embedded.tomcat.TomcatWebServer][initialize][111]-> Tomcat initialized with port 7070 (http)
2025-07-30 21:51:41 [INFO][org.apache.coyote.http11.Http11NioProtocol][log][173]-> Initializing ProtocolHandler ["http-nio-7070"]
2025-07-30 21:51:41 [INFO][org.apache.catalina.core.StandardService][log][173]-> Starting service [Tomcat]
2025-07-30 21:51:41 [INFO][org.apache.catalina.core.StandardEngine][log][173]-> Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-07-30 21:51:41 [INFO][org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/admin]][log][173]-> Initializing Spring embedded WebApplicationContext
2025-07-30 21:51:41 [INFO][org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext][prepareWebApplicationContext][301]-> Root WebApplicationContext: initialization completed in 1383 ms
2025-07-30 21:51:41 [INFO][com.alibaba.druid.spring.boot3.autoconfigure.DruidDataSourceAutoConfigure][dataSource][66]-> Init DruidDataSource
2025-07-30 21:51:41 [INFO][com.alibaba.druid.pool.DruidDataSource][init][1007]-> {dataSource-1} inited
2025-07-30 21:51:43 [INFO][org.apache.coyote.http11.Http11NioProtocol][log][173]-> Starting ProtocolHandler ["http-nio-7070"]
2025-07-30 21:51:43 [INFO][org.springframework.boot.web.embedded.tomcat.TomcatWebServer][start][243]-> Tomcat started on port 7070 (http) with context path '/admin'
2025-07-30 21:51:43 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStarted][59]-> Started BilibiliAdminApplication in 3.849 seconds (process running for 4.819)
2025-07-30 21:52:08 [INFO][org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/admin]][log][173]-> Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 21:52:08 [INFO][org.springframework.web.servlet.DispatcherServlet][initServletBean][532]-> Initializing Servlet 'dispatcherServlet'
2025-07-30 21:52:08 [INFO][org.springframework.web.servlet.DispatcherServlet][initServletBean][554]-> Completed initialization in 1 ms
2025-07-30 21:52:08 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:52:08 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/doc.html, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:17 [INFO][org.springframework.boot.web.embedded.tomcat.GracefulShutdown][shutDownGracefully][54]-> Commencing graceful shutdown. Waiting for active requests to complete
2025-07-30 21:53:17 [INFO][org.springframework.boot.web.embedded.tomcat.GracefulShutdown][doShutdown][76]-> Graceful shutdown complete
2025-07-30 21:53:17 [INFO][com.alibaba.druid.pool.DruidDataSource][close][2212]-> {dataSource-1} closing ...
2025-07-30 21:53:17 [INFO][com.alibaba.druid.pool.DruidDataSource][close][2285]-> {dataSource-1} closed
2025-07-30 21:53:21 [INFO][org.hibernate.validator.internal.util.Version][<clinit>][21]-> HV000001: Hibernate Validator 8.0.2.Final
2025-07-30 21:53:21 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStarting][53]-> Starting BilibiliAdminApplication using Java 21.0.7 with PID 15780 (D:\Workspace\java_code\bilibili-backend\bilibili-admin\target\classes started by 26788 in D:\Workspace\java_code\bilibili-backend)
2025-07-30 21:53:21 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStartupProfileInfo][652]-> No active profile set, falling back to 1 default profile: "default"
2025-07-30 21:53:22 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 21:53:22 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-30 21:53:22 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 10 ms. Found 0 Elasticsearch repository interfaces.
2025-07-30 21:53:22 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 21:53:22 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-30 21:53:22 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 1 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-30 21:53:22 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 21:53:22 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 21:53:22 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 1 ms. Found 0 Redis repository interfaces.
2025-07-30 21:53:22 [INFO][org.springframework.boot.web.embedded.tomcat.TomcatWebServer][initialize][111]-> Tomcat initialized with port 7070 (http)
2025-07-30 21:53:22 [INFO][org.apache.coyote.http11.Http11NioProtocol][log][173]-> Initializing ProtocolHandler ["http-nio-7070"]
2025-07-30 21:53:22 [INFO][org.apache.catalina.core.StandardService][log][173]-> Starting service [Tomcat]
2025-07-30 21:53:22 [INFO][org.apache.catalina.core.StandardEngine][log][173]-> Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-07-30 21:53:22 [INFO][org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/admin]][log][173]-> Initializing Spring embedded WebApplicationContext
2025-07-30 21:53:22 [INFO][org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext][prepareWebApplicationContext][301]-> Root WebApplicationContext: initialization completed in 1319 ms
2025-07-30 21:53:22 [INFO][com.alibaba.druid.spring.boot3.autoconfigure.DruidDataSourceAutoConfigure][dataSource][66]-> Init DruidDataSource
2025-07-30 21:53:23 [INFO][com.alibaba.druid.pool.DruidDataSource][init][1007]-> {dataSource-1} inited
2025-07-30 21:53:24 [INFO][org.apache.coyote.http11.Http11NioProtocol][log][173]-> Starting ProtocolHandler ["http-nio-7070"]
2025-07-30 21:53:24 [INFO][org.springframework.boot.web.embedded.tomcat.TomcatWebServer][start][243]-> Tomcat started on port 7070 (http) with context path '/admin'
2025-07-30 21:53:24 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStarted][59]-> Started BilibiliAdminApplication in 3.387 seconds (process running for 4.241)
2025-07-30 21:53:42 [INFO][org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/admin]][log][173]-> Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 21:53:42 [INFO][org.springframework.web.servlet.DispatcherServlet][initServletBean][532]-> Initializing Servlet 'dispatcherServlet'
2025-07-30 21:53:42 [INFO][org.springframework.web.servlet.DispatcherServlet][initServletBean][554]-> Completed initialization in 2 ms
2025-07-30 21:53:42 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:42 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:42 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:42 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:42 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:42 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/app.b0c0d7df.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:42 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/css/chunk-vendors.f24a310a.css, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:42 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-vendors.d51cf6f8.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:42 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/css/app.8a48e86b.css, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:42 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:42 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/css/chunk-75464e7e.8fb93ba5.css, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:42 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:42 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/css/chunk-d7d5f59c.a9ffbfcb.css, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:42 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:42 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:42 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:42 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-069eb437.355c4fb0.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:42 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:42 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-0fd67716.d57e2c41.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:42 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:42 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:42 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-2d0af44e.392afcd6.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:42 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-2d0bd799.eb48b7f1.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:42 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-260d712a.390177fe.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:42 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-0d102d5a.b2bddffc.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:42 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:42 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:42 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:42 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:42 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-735c675c.5b409314.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:42 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-2d0da532.591ad7fc.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:42 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:42 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-3b888a65.8737ce4f.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:42 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-3ec4aaa8.a79d19f8.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:42 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-589faee0.5bfd1708.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:42 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:43 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-75464e7e.b130271b.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:43 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:43 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:43 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-adb9e944.2c7f24fe.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:43 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-d7d5f59c.e61130f3.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:43 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:43 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/favicon.ico, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:46 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:46 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:46 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:46 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:46 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:46 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/css/app.8a48e86b.css, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:46 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/app.b0c0d7df.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:46 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-vendors.d51cf6f8.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:46 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/css/chunk-vendors.f24a310a.css, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:46 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:46 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/css/chunk-75464e7e.8fb93ba5.css, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:46 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:46 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:46 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:46 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-0d102d5a.b2bddffc.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:46 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-069eb437.355c4fb0.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:46 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/css/chunk-d7d5f59c.a9ffbfcb.css, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:46 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:46 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:46 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-0fd67716.d57e2c41.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:46 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-260d712a.390177fe.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:46 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:46 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:46 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:46 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-2d0af44e.392afcd6.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:46 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:46 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-2d0bd799.eb48b7f1.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:46 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:46 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-589faee0.5bfd1708.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:46 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-3b888a65.8737ce4f.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:46 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:46 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-2d0da532.591ad7fc.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:46 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-3ec4aaa8.a79d19f8.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:46 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:46 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/favicon.ico, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:46 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:46 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:46 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-735c675c.5b409314.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:46 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-75464e7e.b130271b.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:46 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:46 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-d7d5f59c.e61130f3.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:53:46 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:53:46 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-adb9e944.2c7f24fe.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:56:16 [INFO][org.springframework.boot.web.embedded.tomcat.GracefulShutdown][shutDownGracefully][54]-> Commencing graceful shutdown. Waiting for active requests to complete
2025-07-30 21:56:16 [INFO][org.springframework.boot.web.embedded.tomcat.GracefulShutdown][doShutdown][76]-> Graceful shutdown complete
2025-07-30 21:56:16 [INFO][com.alibaba.druid.pool.DruidDataSource][close][2212]-> {dataSource-1} closing ...
2025-07-30 21:56:16 [INFO][com.alibaba.druid.pool.DruidDataSource][close][2285]-> {dataSource-1} closed
2025-07-30 21:56:19 [INFO][org.hibernate.validator.internal.util.Version][<clinit>][21]-> HV000001: Hibernate Validator 8.0.2.Final
2025-07-30 21:56:20 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStarting][53]-> Starting BilibiliAdminApplication using Java 21.0.7 with PID 13072 (D:\Workspace\java_code\bilibili-backend\bilibili-admin\target\classes started by 26788 in D:\Workspace\java_code\bilibili-backend)
2025-07-30 21:56:20 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStartupProfileInfo][652]-> No active profile set, falling back to 1 default profile: "default"
2025-07-30 21:56:20 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 21:56:20 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-30 21:56:20 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 10 ms. Found 0 Elasticsearch repository interfaces.
2025-07-30 21:56:20 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 21:56:20 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-30 21:56:20 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 0 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-30 21:56:20 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 21:56:20 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 21:56:20 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 0 ms. Found 0 Redis repository interfaces.
2025-07-30 21:56:21 [INFO][org.springframework.boot.web.embedded.tomcat.TomcatWebServer][initialize][111]-> Tomcat initialized with port 7070 (http)
2025-07-30 21:56:21 [INFO][org.apache.coyote.http11.Http11NioProtocol][log][173]-> Initializing ProtocolHandler ["http-nio-7070"]
2025-07-30 21:56:21 [INFO][org.apache.catalina.core.StandardService][log][173]-> Starting service [Tomcat]
2025-07-30 21:56:21 [INFO][org.apache.catalina.core.StandardEngine][log][173]-> Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-07-30 21:56:21 [INFO][org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/admin]][log][173]-> Initializing Spring embedded WebApplicationContext
2025-07-30 21:56:21 [INFO][org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext][prepareWebApplicationContext][301]-> Root WebApplicationContext: initialization completed in 1579 ms
2025-07-30 21:56:21 [INFO][com.alibaba.druid.spring.boot3.autoconfigure.DruidDataSourceAutoConfigure][dataSource][66]-> Init DruidDataSource
2025-07-30 21:56:21 [INFO][com.alibaba.druid.pool.DruidDataSource][init][1007]-> {dataSource-1} inited
2025-07-30 21:56:23 [INFO][org.apache.coyote.http11.Http11NioProtocol][log][173]-> Starting ProtocolHandler ["http-nio-7070"]
2025-07-30 21:56:23 [INFO][org.springframework.boot.web.embedded.tomcat.TomcatWebServer][start][243]-> Tomcat started on port 7070 (http) with context path '/admin'
2025-07-30 21:56:23 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStarted][59]-> Started BilibiliAdminApplication in 3.838 seconds (process running for 4.749)
2025-07-30 21:56:32 [INFO][org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/admin]][log][173]-> Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 21:56:32 [INFO][org.springframework.web.servlet.DispatcherServlet][initServletBean][532]-> Initializing Servlet 'dispatcherServlet'
2025-07-30 21:56:32 [INFO][org.springframework.web.servlet.DispatcherServlet][initServletBean][554]-> Completed initialization in 2 ms
2025-07-30 21:56:32 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:56:32 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:56:32 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:56:32 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:56:32 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/app.b0c0d7df.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:56:32 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/css/app.8a48e86b.css, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:56:32 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-vendors.d51cf6f8.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:56:32 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/css/chunk-vendors.f24a310a.css, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:56:32 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:56:32 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:56:32 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/css/chunk-d7d5f59c.a9ffbfcb.css, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:56:32 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:56:32 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:56:32 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-069eb437.355c4fb0.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:56:32 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-0d102d5a.b2bddffc.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:56:32 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:56:32 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:56:32 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/css/chunk-75464e7e.8fb93ba5.css, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:56:32 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-260d712a.390177fe.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:56:32 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-0fd67716.d57e2c41.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:56:32 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:56:32 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:56:32 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:56:32 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-3ec4aaa8.a79d19f8.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:56:32 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:56:32 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-2d0da532.591ad7fc.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:56:32 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:56:32 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-3b888a65.8737ce4f.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:56:32 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-2d0bd799.eb48b7f1.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:56:32 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-2d0af44e.392afcd6.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:56:32 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:56:32 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-589faee0.5bfd1708.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:56:32 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:56:32 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:56:32 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:56:32 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:56:32 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-adb9e944.2c7f24fe.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:56:32 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-d7d5f59c.e61130f3.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:56:32 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-735c675c.5b409314.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:56:32 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-75464e7e.b130271b.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:56:32 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:56:32 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/favicon.ico, 错误信息:
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:73)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-30 21:56:54 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:05 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:05 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:22 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:22 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:22 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:22 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:22 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/css/chunk-d7d5f59c.a9ffbfcb.css, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:57:22 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/css/app.8a48e86b.css, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:57:22 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-vendors.d51cf6f8.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:57:22 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/css/chunk-75464e7e.8fb93ba5.css, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:57:22 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/app.b0c0d7df.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:57:22 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/css/chunk-vendors.f24a310a.css, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:57:22 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:22 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:22 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:22 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:22 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:22 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-0d102d5a.b2bddffc.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:57:22 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:22 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-069eb437.355c4fb0.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:57:22 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-260d712a.390177fe.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:57:22 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-2d0af44e.392afcd6.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:57:22 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-2d0bd799.eb48b7f1.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:57:22 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-0fd67716.d57e2c41.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:57:22 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:22 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:22 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:22 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:22 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-2d0da532.591ad7fc.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:57:22 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:22 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-3b888a65.8737ce4f.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:57:22 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-735c675c.5b409314.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:57:22 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:22 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-589faee0.5bfd1708.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:57:22 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-3ec4aaa8.a79d19f8.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:57:22 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-75464e7e.b130271b.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:57:22 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:22 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:22 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-adb9e944.2c7f24fe.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:57:22 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/webjars/js/chunk-d7d5f59c.e61130f3.js, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:57:22 [INFO][org.springframework.boot.web.embedded.tomcat.GracefulShutdown][shutDownGracefully][54]-> Commencing graceful shutdown. Waiting for active requests to complete
2025-07-30 21:57:22 [INFO][org.springframework.boot.web.embedded.tomcat.GracefulShutdown][doShutdown][76]-> Graceful shutdown complete
2025-07-30 21:57:22 [INFO][com.alibaba.druid.pool.DruidDataSource][close][2212]-> {dataSource-1} closing ...
2025-07-30 21:57:22 [INFO][com.alibaba.druid.pool.DruidDataSource][close][2285]-> {dataSource-1} closed
2025-07-30 21:57:26 [INFO][org.hibernate.validator.internal.util.Version][<clinit>][21]-> HV000001: Hibernate Validator 8.0.2.Final
2025-07-30 21:57:26 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStarting][53]-> Starting BilibiliAdminApplication using Java 21.0.7 with PID 22332 (D:\Workspace\java_code\bilibili-backend\bilibili-admin\target\classes started by 26788 in D:\Workspace\java_code\bilibili-backend)
2025-07-30 21:57:26 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStartupProfileInfo][652]-> No active profile set, falling back to 1 default profile: "default"
2025-07-30 21:57:26 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 21:57:26 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-30 21:57:27 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 10 ms. Found 0 Elasticsearch repository interfaces.
2025-07-30 21:57:27 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 21:57:27 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-30 21:57:27 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 1 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-30 21:57:27 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 21:57:27 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 21:57:27 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 0 ms. Found 0 Redis repository interfaces.
2025-07-30 21:57:27 [INFO][org.springframework.boot.web.embedded.tomcat.TomcatWebServer][initialize][111]-> Tomcat initialized with port 7070 (http)
2025-07-30 21:57:27 [INFO][org.apache.coyote.http11.Http11NioProtocol][log][173]-> Initializing ProtocolHandler ["http-nio-7070"]
2025-07-30 21:57:27 [INFO][org.apache.catalina.core.StandardService][log][173]-> Starting service [Tomcat]
2025-07-30 21:57:27 [INFO][org.apache.catalina.core.StandardEngine][log][173]-> Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-07-30 21:57:27 [INFO][org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/admin]][log][173]-> Initializing Spring embedded WebApplicationContext
2025-07-30 21:57:27 [INFO][org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext][prepareWebApplicationContext][301]-> Root WebApplicationContext: initialization completed in 1404 ms
2025-07-30 21:57:27 [INFO][com.alibaba.druid.spring.boot3.autoconfigure.DruidDataSourceAutoConfigure][dataSource][66]-> Init DruidDataSource
2025-07-30 21:57:28 [INFO][com.alibaba.druid.pool.DruidDataSource][init][1007]-> {dataSource-1} inited
2025-07-30 21:57:29 [INFO][org.apache.coyote.http11.Http11NioProtocol][log][173]-> Starting ProtocolHandler ["http-nio-7070"]
2025-07-30 21:57:29 [INFO][org.springframework.boot.web.embedded.tomcat.TomcatWebServer][start][243]-> Tomcat started on port 7070 (http) with context path '/admin'
2025-07-30 21:57:29 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStarted][59]-> Started BilibiliAdminApplication in 3.69 seconds (process running for 4.738)
2025-07-30 21:57:30 [INFO][org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/admin]][log][173]-> Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 21:57:30 [INFO][org.springframework.web.servlet.DispatcherServlet][initServletBean][532]-> Initializing Servlet 'dispatcherServlet'
2025-07-30 21:57:30 [INFO][org.springframework.web.servlet.DispatcherServlet][initServletBean][554]-> Completed initialization in 2 ms
2025-07-30 21:57:30 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:37 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:42 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:42 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:42 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:43 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:43 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:43 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:43 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:43 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:43 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:43 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:43 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:43 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:43 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:43 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:43 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:43 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:43 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:43 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:43 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:43 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:43 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:43 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:43 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/favicon.ico, 错误信息:
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:73)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-30 21:57:43 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:57:43 [WARN][com.bilibili.common.exception.GlobalExceptionHandler][handleBusinessException][46]-> 业务异常，请求路径: /admin/v3/api-docs/default, 错误码: 901, 错误信息: 登录状态已失效，请重新登录
2025-07-30 21:58:38 [INFO][org.springframework.boot.web.embedded.tomcat.GracefulShutdown][shutDownGracefully][54]-> Commencing graceful shutdown. Waiting for active requests to complete
2025-07-30 21:58:38 [INFO][org.springframework.boot.web.embedded.tomcat.GracefulShutdown][doShutdown][76]-> Graceful shutdown complete
2025-07-30 21:58:38 [INFO][com.alibaba.druid.pool.DruidDataSource][close][2212]-> {dataSource-1} closing ...
2025-07-30 21:58:38 [INFO][com.alibaba.druid.pool.DruidDataSource][close][2285]-> {dataSource-1} closed
2025-07-30 21:58:41 [INFO][org.hibernate.validator.internal.util.Version][<clinit>][21]-> HV000001: Hibernate Validator 8.0.2.Final
2025-07-30 21:58:41 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStarting][53]-> Starting BilibiliAdminApplication using Java 21.0.7 with PID 15964 (D:\Workspace\java_code\bilibili-backend\bilibili-admin\target\classes started by 26788 in D:\Workspace\java_code\bilibili-backend)
2025-07-30 21:58:41 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStartupProfileInfo][652]-> No active profile set, falling back to 1 default profile: "default"
2025-07-30 21:58:42 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 21:58:42 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-30 21:58:42 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 11 ms. Found 0 Elasticsearch repository interfaces.
2025-07-30 21:58:42 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 21:58:42 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-30 21:58:42 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 0 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-30 21:58:42 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 21:58:42 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 21:58:42 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 1 ms. Found 0 Redis repository interfaces.
2025-07-30 21:58:43 [INFO][org.springframework.boot.web.embedded.tomcat.TomcatWebServer][initialize][111]-> Tomcat initialized with port 7070 (http)
2025-07-30 21:58:43 [INFO][org.apache.coyote.http11.Http11NioProtocol][log][173]-> Initializing ProtocolHandler ["http-nio-7070"]
2025-07-30 21:58:43 [INFO][org.apache.catalina.core.StandardService][log][173]-> Starting service [Tomcat]
2025-07-30 21:58:43 [INFO][org.apache.catalina.core.StandardEngine][log][173]-> Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-07-30 21:58:43 [INFO][org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/admin]][log][173]-> Initializing Spring embedded WebApplicationContext
2025-07-30 21:58:43 [INFO][org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext][prepareWebApplicationContext][301]-> Root WebApplicationContext: initialization completed in 1297 ms
2025-07-30 21:58:43 [INFO][com.alibaba.druid.spring.boot3.autoconfigure.DruidDataSourceAutoConfigure][dataSource][66]-> Init DruidDataSource
2025-07-30 21:58:43 [INFO][com.alibaba.druid.pool.DruidDataSource][init][1007]-> {dataSource-1} inited
2025-07-30 21:58:44 [INFO][org.apache.coyote.http11.Http11NioProtocol][log][173]-> Starting ProtocolHandler ["http-nio-7070"]
2025-07-30 21:58:44 [INFO][org.springframework.boot.web.embedded.tomcat.TomcatWebServer][start][243]-> Tomcat started on port 7070 (http) with context path '/admin'
2025-07-30 21:58:44 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStarted][59]-> Started BilibiliAdminApplication in 3.382 seconds (process running for 4.16)
2025-07-30 21:58:52 [INFO][org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/admin]][log][173]-> Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 21:58:52 [INFO][org.springframework.web.servlet.DispatcherServlet][initServletBean][532]-> Initializing Servlet 'dispatcherServlet'
2025-07-30 21:58:52 [INFO][org.springframework.web.servlet.DispatcherServlet][initServletBean][554]-> Completed initialization in 1 ms
2025-07-30 21:58:52 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:58:52 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:58:52 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:58:52 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/favicon.ico, 错误信息:
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:73)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-30 21:58:52 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:58:52 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/v3/api-docs/default, 错误信息:
jakarta.servlet.ServletException: Handler dispatch failed: java.lang.NoSuchMethodError: 'void org.springframework.web.method.ControllerAdviceBean.<init>(java.lang.Object)'
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1104)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NoSuchMethodError: 'void org.springframework.web.method.ControllerAdviceBean.<init>(java.lang.Object)'
	at org.springdoc.core.service.GenericResponseService.lambda$getGenericMapResponse$8(GenericResponseService.java:702)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:178)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:1024)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at org.springdoc.core.service.GenericResponseService.getGenericMapResponse(GenericResponseService.java:704)
	at org.springdoc.core.service.GenericResponseService.build(GenericResponseService.java:246)
	at org.springdoc.api.AbstractOpenApiResource.calculatePath(AbstractOpenApiResource.java:499)
	at org.springdoc.api.AbstractOpenApiResource.calculatePath(AbstractOpenApiResource.java:676)
	at org.springdoc.webmvc.api.OpenApiResource.lambda$calculatePath$11(OpenApiResource.java:219)
	at java.base/java.util.Optional.ifPresent(Optional.java:178)
	at org.springdoc.webmvc.api.OpenApiResource.calculatePath(OpenApiResource.java:200)
	at org.springdoc.webmvc.api.OpenApiResource.lambda$getPaths$2(OpenApiResource.java:170)
	at java.base/java.util.Optional.ifPresent(Optional.java:178)
	at org.springdoc.webmvc.api.OpenApiResource.getPaths(OpenApiResource.java:149)
	at org.springdoc.api.AbstractOpenApiResource.getOpenApi(AbstractOpenApiResource.java:353)
	at org.springdoc.webmvc.api.OpenApiResource.openapiJson(OpenApiResource.java:124)
	at org.springdoc.webmvc.api.OpenApiWebMvcResource.openapiJson(OpenApiWebMvcResource.java:111)
	at org.springdoc.webmvc.api.MultipleOpenApiWebMvcResource.openapiJson(MultipleOpenApiWebMvcResource.java:95)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	... 42 common frames omitted
2025-07-30 21:58:56 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:58:56 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/favicon.ico, 错误信息:
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:73)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-30 21:58:57 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:58:57 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/favicon.ico, 错误信息:
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:73)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-30 21:59:01 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:59:01 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/favicon.ico, 错误信息:
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:73)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-30 21:59:02 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:59:02 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/favicon.ico, 错误信息:
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:73)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-30 21:59:02 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:59:02 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/favicon.ico, 错误信息:
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:73)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-30 21:59:56 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:59:56 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:59:56 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/favicon.ico, 错误信息:
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:73)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-30 21:59:56 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:59:56 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 21:59:56 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/v3/api-docs/default, 错误信息:
jakarta.servlet.ServletException: Handler dispatch failed: java.lang.NoSuchMethodError: 'void org.springframework.web.method.ControllerAdviceBean.<init>(java.lang.Object)'
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1104)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NoSuchMethodError: 'void org.springframework.web.method.ControllerAdviceBean.<init>(java.lang.Object)'
	at org.springdoc.core.service.GenericResponseService.lambda$getGenericMapResponse$8(GenericResponseService.java:702)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:178)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:1024)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at org.springdoc.core.service.GenericResponseService.getGenericMapResponse(GenericResponseService.java:704)
	at org.springdoc.core.service.GenericResponseService.build(GenericResponseService.java:246)
	at org.springdoc.api.AbstractOpenApiResource.calculatePath(AbstractOpenApiResource.java:499)
	at org.springdoc.api.AbstractOpenApiResource.calculatePath(AbstractOpenApiResource.java:676)
	at org.springdoc.webmvc.api.OpenApiResource.lambda$calculatePath$11(OpenApiResource.java:219)
	at java.base/java.util.Optional.ifPresent(Optional.java:178)
	at org.springdoc.webmvc.api.OpenApiResource.calculatePath(OpenApiResource.java:200)
	at org.springdoc.webmvc.api.OpenApiResource.lambda$getPaths$2(OpenApiResource.java:170)
	at java.base/java.util.Optional.ifPresent(Optional.java:178)
	at org.springdoc.webmvc.api.OpenApiResource.getPaths(OpenApiResource.java:149)
	at org.springdoc.api.AbstractOpenApiResource.getOpenApi(AbstractOpenApiResource.java:353)
	at org.springdoc.webmvc.api.OpenApiResource.openapiJson(OpenApiResource.java:124)
	at org.springdoc.webmvc.api.OpenApiWebMvcResource.openapiJson(OpenApiWebMvcResource.java:111)
	at org.springdoc.webmvc.api.MultipleOpenApiWebMvcResource.openapiJson(MultipleOpenApiWebMvcResource.java:95)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	... 42 common frames omitted
2025-07-30 22:00:03 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 22:00:03 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 22:00:03 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/favicon.ico, 错误信息:
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:73)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-30 22:00:03 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 22:00:03 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 22:00:03 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/v3/api-docs/default, 错误信息:
jakarta.servlet.ServletException: Handler dispatch failed: java.lang.NoSuchMethodError: 'void org.springframework.web.method.ControllerAdviceBean.<init>(java.lang.Object)'
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1104)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NoSuchMethodError: 'void org.springframework.web.method.ControllerAdviceBean.<init>(java.lang.Object)'
	at org.springdoc.core.service.GenericResponseService.lambda$getGenericMapResponse$8(GenericResponseService.java:702)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:178)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:1024)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at org.springdoc.core.service.GenericResponseService.getGenericMapResponse(GenericResponseService.java:704)
	at org.springdoc.core.service.GenericResponseService.build(GenericResponseService.java:246)
	at org.springdoc.api.AbstractOpenApiResource.calculatePath(AbstractOpenApiResource.java:499)
	at org.springdoc.api.AbstractOpenApiResource.calculatePath(AbstractOpenApiResource.java:676)
	at org.springdoc.webmvc.api.OpenApiResource.lambda$calculatePath$11(OpenApiResource.java:219)
	at java.base/java.util.Optional.ifPresent(Optional.java:178)
	at org.springdoc.webmvc.api.OpenApiResource.calculatePath(OpenApiResource.java:200)
	at org.springdoc.webmvc.api.OpenApiResource.lambda$getPaths$2(OpenApiResource.java:170)
	at java.base/java.util.Optional.ifPresent(Optional.java:178)
	at org.springdoc.webmvc.api.OpenApiResource.getPaths(OpenApiResource.java:149)
	at org.springdoc.api.AbstractOpenApiResource.getOpenApi(AbstractOpenApiResource.java:353)
	at org.springdoc.webmvc.api.OpenApiResource.openapiJson(OpenApiResource.java:124)
	at org.springdoc.webmvc.api.OpenApiWebMvcResource.openapiJson(OpenApiWebMvcResource.java:111)
	at org.springdoc.webmvc.api.MultipleOpenApiWebMvcResource.openapiJson(MultipleOpenApiWebMvcResource.java:95)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	... 42 common frames omitted
2025-07-30 22:00:54 [INFO][org.springframework.boot.web.embedded.tomcat.GracefulShutdown][shutDownGracefully][54]-> Commencing graceful shutdown. Waiting for active requests to complete
2025-07-30 22:00:54 [INFO][org.springframework.boot.web.embedded.tomcat.GracefulShutdown][doShutdown][76]-> Graceful shutdown complete
2025-07-30 22:00:54 [INFO][com.alibaba.druid.pool.DruidDataSource][close][2212]-> {dataSource-1} closing ...
2025-07-30 22:00:54 [INFO][com.alibaba.druid.pool.DruidDataSource][close][2285]-> {dataSource-1} closed
2025-07-30 22:00:58 [INFO][org.hibernate.validator.internal.util.Version][<clinit>][21]-> HV000001: Hibernate Validator 8.0.2.Final
2025-07-30 22:00:58 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStarting][53]-> Starting BilibiliAdminApplication using Java 21.0.7 with PID 12388 (D:\Workspace\java_code\bilibili-backend\bilibili-admin\target\classes started by 26788 in D:\Workspace\java_code\bilibili-backend)
2025-07-30 22:00:58 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStartupProfileInfo][652]-> No active profile set, falling back to 1 default profile: "default"
2025-07-30 22:00:58 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 22:00:58 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-30 22:00:59 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 11 ms. Found 0 Elasticsearch repository interfaces.
2025-07-30 22:00:59 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 22:00:59 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-30 22:00:59 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 1 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-30 22:00:59 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 22:00:59 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 22:00:59 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 0 ms. Found 0 Redis repository interfaces.
2025-07-30 22:00:59 [INFO][org.springframework.boot.web.embedded.tomcat.TomcatWebServer][initialize][111]-> Tomcat initialized with port 7070 (http)
2025-07-30 22:00:59 [INFO][org.apache.coyote.http11.Http11NioProtocol][log][173]-> Initializing ProtocolHandler ["http-nio-7070"]
2025-07-30 22:00:59 [INFO][org.apache.catalina.core.StandardService][log][173]-> Starting service [Tomcat]
2025-07-30 22:00:59 [INFO][org.apache.catalina.core.StandardEngine][log][173]-> Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-07-30 22:00:59 [INFO][org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/admin]][log][173]-> Initializing Spring embedded WebApplicationContext
2025-07-30 22:00:59 [INFO][org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext][prepareWebApplicationContext][301]-> Root WebApplicationContext: initialization completed in 1466 ms
2025-07-30 22:00:59 [INFO][com.alibaba.druid.spring.boot3.autoconfigure.DruidDataSourceAutoConfigure][dataSource][66]-> Init DruidDataSource
2025-07-30 22:01:00 [INFO][com.alibaba.druid.pool.DruidDataSource][init][1007]-> {dataSource-1} inited
2025-07-30 22:01:00 [WARN][org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext][refresh][635]-> Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'webConfig': Injection of resource dependencies failed
2025-07-30 22:01:00 [INFO][com.alibaba.druid.pool.DruidDataSource][close][2212]-> {dataSource-1} closing ...
2025-07-30 22:01:00 [INFO][com.alibaba.druid.pool.DruidDataSource][close][2285]-> {dataSource-1} closed
2025-07-30 22:01:00 [INFO][org.apache.catalina.core.StandardService][log][173]-> Stopping service [Tomcat]
2025-07-30 22:01:00 [INFO][org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLogger][logMessage][82]-> 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-30 22:01:00 [ERROR][org.springframework.boot.diagnostics.LoggingFailureAnalysisReporter][report][40]-> 

***************************
APPLICATION FAILED TO START
***************************

Description:

A component required a bean of type 'com.bilibili.common.interceptor.LoginInterceptor' that could not be found.


Action:

Consider defining a bean of type 'com.bilibili.common.interceptor.LoginInterceptor' in your configuration.

2025-07-30 22:01:47 [INFO][org.hibernate.validator.internal.util.Version][<clinit>][21]-> HV000001: Hibernate Validator 8.0.2.Final
2025-07-30 22:01:47 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStarting][53]-> Starting BilibiliAdminApplication using Java 21.0.7 with PID 15428 (D:\Workspace\java_code\bilibili-backend\bilibili-admin\target\classes started by 26788 in D:\Workspace\java_code\bilibili-backend)
2025-07-30 22:01:47 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStartupProfileInfo][652]-> No active profile set, falling back to 1 default profile: "default"
2025-07-30 22:01:48 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 22:01:48 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-30 22:01:48 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 9 ms. Found 0 Elasticsearch repository interfaces.
2025-07-30 22:01:48 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 22:01:48 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-30 22:01:48 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 0 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-30 22:01:48 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 22:01:48 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 22:01:48 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 0 ms. Found 0 Redis repository interfaces.
2025-07-30 22:01:48 [INFO][org.springframework.boot.web.embedded.tomcat.TomcatWebServer][initialize][111]-> Tomcat initialized with port 7070 (http)
2025-07-30 22:01:48 [INFO][org.apache.coyote.http11.Http11NioProtocol][log][173]-> Initializing ProtocolHandler ["http-nio-7070"]
2025-07-30 22:01:48 [INFO][org.apache.catalina.core.StandardService][log][173]-> Starting service [Tomcat]
2025-07-30 22:01:48 [INFO][org.apache.catalina.core.StandardEngine][log][173]-> Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-07-30 22:01:48 [INFO][org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/admin]][log][173]-> Initializing Spring embedded WebApplicationContext
2025-07-30 22:01:48 [INFO][org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext][prepareWebApplicationContext][301]-> Root WebApplicationContext: initialization completed in 1297 ms
2025-07-30 22:01:49 [INFO][com.alibaba.druid.spring.boot3.autoconfigure.DruidDataSourceAutoConfigure][dataSource][66]-> Init DruidDataSource
2025-07-30 22:01:49 [INFO][com.alibaba.druid.pool.DruidDataSource][init][1007]-> {dataSource-1} inited
2025-07-30 22:01:50 [INFO][org.apache.coyote.http11.Http11NioProtocol][log][173]-> Starting ProtocolHandler ["http-nio-7070"]
2025-07-30 22:01:50 [INFO][org.springframework.boot.web.embedded.tomcat.TomcatWebServer][start][243]-> Tomcat started on port 7070 (http) with context path '/admin'
2025-07-30 22:01:50 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStarted][59]-> Started BilibiliAdminApplication in 3.363 seconds (process running for 4.155)
2025-07-30 22:01:55 [INFO][org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/admin]][log][173]-> Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 22:01:55 [INFO][org.springframework.web.servlet.DispatcherServlet][initServletBean][532]-> Initializing Servlet 'dispatcherServlet'
2025-07-30 22:01:55 [INFO][org.springframework.web.servlet.DispatcherServlet][initServletBean][554]-> Completed initialization in 0 ms
2025-07-30 22:01:55 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 22:01:55 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 22:01:55 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 22:01:55 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/favicon.ico, 错误信息:
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:73)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-30 22:01:55 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 22:01:56 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/v3/api-docs/default, 错误信息:
jakarta.servlet.ServletException: Handler dispatch failed: java.lang.NoSuchMethodError: 'void org.springframework.web.method.ControllerAdviceBean.<init>(java.lang.Object)'
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1104)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NoSuchMethodError: 'void org.springframework.web.method.ControllerAdviceBean.<init>(java.lang.Object)'
	at org.springdoc.core.service.GenericResponseService.lambda$getGenericMapResponse$8(GenericResponseService.java:702)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:178)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:1024)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at org.springdoc.core.service.GenericResponseService.getGenericMapResponse(GenericResponseService.java:704)
	at org.springdoc.core.service.GenericResponseService.build(GenericResponseService.java:246)
	at org.springdoc.api.AbstractOpenApiResource.calculatePath(AbstractOpenApiResource.java:499)
	at org.springdoc.api.AbstractOpenApiResource.calculatePath(AbstractOpenApiResource.java:676)
	at org.springdoc.webmvc.api.OpenApiResource.lambda$calculatePath$11(OpenApiResource.java:219)
	at java.base/java.util.Optional.ifPresent(Optional.java:178)
	at org.springdoc.webmvc.api.OpenApiResource.calculatePath(OpenApiResource.java:200)
	at org.springdoc.webmvc.api.OpenApiResource.lambda$getPaths$2(OpenApiResource.java:170)
	at java.base/java.util.Optional.ifPresent(Optional.java:178)
	at org.springdoc.webmvc.api.OpenApiResource.getPaths(OpenApiResource.java:149)
	at org.springdoc.api.AbstractOpenApiResource.getOpenApi(AbstractOpenApiResource.java:353)
	at org.springdoc.webmvc.api.OpenApiResource.openapiJson(OpenApiResource.java:124)
	at org.springdoc.webmvc.api.OpenApiWebMvcResource.openapiJson(OpenApiWebMvcResource.java:111)
	at org.springdoc.webmvc.api.MultipleOpenApiWebMvcResource.openapiJson(MultipleOpenApiWebMvcResource.java:95)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	... 42 common frames omitted
2025-07-30 22:01:58 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 22:01:58 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/favicon.ico, 错误信息:
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:73)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-30 22:02:00 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 22:02:00 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/favicon.ico, 错误信息:
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:73)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-30 22:06:08 [INFO][org.springframework.boot.web.embedded.tomcat.GracefulShutdown][shutDownGracefully][54]-> Commencing graceful shutdown. Waiting for active requests to complete
2025-07-30 22:06:08 [INFO][org.springframework.boot.web.embedded.tomcat.GracefulShutdown][doShutdown][76]-> Graceful shutdown complete
2025-07-30 22:06:08 [INFO][com.alibaba.druid.pool.DruidDataSource][close][2212]-> {dataSource-1} closing ...
2025-07-30 22:06:08 [INFO][com.alibaba.druid.pool.DruidDataSource][close][2285]-> {dataSource-1} closed
2025-07-30 22:06:12 [INFO][org.hibernate.validator.internal.util.Version][<clinit>][21]-> HV000001: Hibernate Validator 8.0.2.Final
2025-07-30 22:06:12 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStarting][53]-> Starting BilibiliAdminApplication using Java 21.0.7 with PID 23864 (D:\Workspace\java_code\bilibili-backend\bilibili-admin\target\classes started by 26788 in D:\Workspace\java_code\bilibili-backend)
2025-07-30 22:06:12 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStartupProfileInfo][652]-> No active profile set, falling back to 1 default profile: "default"
2025-07-30 22:06:13 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 22:06:13 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-30 22:06:13 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 9 ms. Found 0 Elasticsearch repository interfaces.
2025-07-30 22:06:13 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 22:06:13 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-30 22:06:13 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 0 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-30 22:06:13 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 22:06:13 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 22:06:13 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 0 ms. Found 0 Redis repository interfaces.
2025-07-30 22:06:13 [INFO][org.springframework.boot.web.embedded.tomcat.TomcatWebServer][initialize][111]-> Tomcat initialized with port 7070 (http)
2025-07-30 22:06:13 [INFO][org.apache.coyote.http11.Http11NioProtocol][log][173]-> Initializing ProtocolHandler ["http-nio-7070"]
2025-07-30 22:06:13 [INFO][org.apache.catalina.core.StandardService][log][173]-> Starting service [Tomcat]
2025-07-30 22:06:13 [INFO][org.apache.catalina.core.StandardEngine][log][173]-> Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-07-30 22:06:13 [INFO][org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/admin]][log][173]-> Initializing Spring embedded WebApplicationContext
2025-07-30 22:06:13 [INFO][org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext][prepareWebApplicationContext][301]-> Root WebApplicationContext: initialization completed in 1300 ms
2025-07-30 22:06:14 [INFO][com.alibaba.druid.spring.boot3.autoconfigure.DruidDataSourceAutoConfigure][dataSource][66]-> Init DruidDataSource
2025-07-30 22:06:14 [INFO][com.alibaba.druid.pool.DruidDataSource][init][1007]-> {dataSource-1} inited
2025-07-30 22:06:15 [INFO][org.apache.coyote.http11.Http11NioProtocol][log][173]-> Starting ProtocolHandler ["http-nio-7070"]
2025-07-30 22:06:15 [INFO][org.springframework.boot.web.embedded.tomcat.TomcatWebServer][start][243]-> Tomcat started on port 7070 (http) with context path '/admin'
2025-07-30 22:06:15 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStarted][59]-> Started BilibiliAdminApplication in 3.349 seconds (process running for 4.212)
2025-07-30 22:06:20 [INFO][org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/admin]][log][173]-> Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 22:06:20 [INFO][org.springframework.web.servlet.DispatcherServlet][initServletBean][532]-> Initializing Servlet 'dispatcherServlet'
2025-07-30 22:06:20 [INFO][org.springframework.web.servlet.DispatcherServlet][initServletBean][554]-> Completed initialization in 1 ms
2025-07-30 22:06:20 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 22:06:21 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 22:06:21 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 22:06:21 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/favicon.ico, 错误信息:
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:73)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-30 22:06:21 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 22:06:21 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/v3/api-docs/default, 错误信息:
jakarta.servlet.ServletException: Handler dispatch failed: java.lang.NoSuchMethodError: 'void org.springframework.web.method.ControllerAdviceBean.<init>(java.lang.Object)'
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1104)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NoSuchMethodError: 'void org.springframework.web.method.ControllerAdviceBean.<init>(java.lang.Object)'
	at org.springdoc.core.service.GenericResponseService.lambda$getGenericMapResponse$8(GenericResponseService.java:702)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:178)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:1024)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at org.springdoc.core.service.GenericResponseService.getGenericMapResponse(GenericResponseService.java:704)
	at org.springdoc.core.service.GenericResponseService.build(GenericResponseService.java:246)
	at org.springdoc.api.AbstractOpenApiResource.calculatePath(AbstractOpenApiResource.java:499)
	at org.springdoc.api.AbstractOpenApiResource.calculatePath(AbstractOpenApiResource.java:676)
	at org.springdoc.webmvc.api.OpenApiResource.lambda$calculatePath$11(OpenApiResource.java:219)
	at java.base/java.util.Optional.ifPresent(Optional.java:178)
	at org.springdoc.webmvc.api.OpenApiResource.calculatePath(OpenApiResource.java:200)
	at org.springdoc.webmvc.api.OpenApiResource.lambda$getPaths$2(OpenApiResource.java:170)
	at java.base/java.util.Optional.ifPresent(Optional.java:178)
	at org.springdoc.webmvc.api.OpenApiResource.getPaths(OpenApiResource.java:149)
	at org.springdoc.api.AbstractOpenApiResource.getOpenApi(AbstractOpenApiResource.java:353)
	at org.springdoc.webmvc.api.OpenApiResource.openapiJson(OpenApiResource.java:124)
	at org.springdoc.webmvc.api.OpenApiWebMvcResource.openapiJson(OpenApiWebMvcResource.java:111)
	at org.springdoc.webmvc.api.MultipleOpenApiWebMvcResource.openapiJson(MultipleOpenApiWebMvcResource.java:95)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	... 42 common frames omitted
2025-07-30 22:07:46 [INFO][org.springframework.boot.web.embedded.tomcat.GracefulShutdown][shutDownGracefully][54]-> Commencing graceful shutdown. Waiting for active requests to complete
2025-07-30 22:07:46 [INFO][org.springframework.boot.web.embedded.tomcat.GracefulShutdown][doShutdown][76]-> Graceful shutdown complete
2025-07-30 22:07:46 [INFO][com.alibaba.druid.pool.DruidDataSource][close][2212]-> {dataSource-1} closing ...
2025-07-30 22:07:46 [INFO][com.alibaba.druid.pool.DruidDataSource][close][2285]-> {dataSource-1} closed
2025-07-30 22:07:52 [INFO][org.hibernate.validator.internal.util.Version][<clinit>][21]-> HV000001: Hibernate Validator 8.0.2.Final
2025-07-30 22:07:52 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStarting][53]-> Starting BilibiliAdminApplication using Java 21.0.7 with PID 21980 (D:\Workspace\java_code\bilibili-backend\bilibili-admin\target\classes started by 26788 in D:\Workspace\java_code\bilibili-backend)
2025-07-30 22:07:52 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStartupProfileInfo][652]-> No active profile set, falling back to 1 default profile: "default"
2025-07-30 22:07:53 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 22:07:53 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-30 22:07:53 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 13 ms. Found 0 Elasticsearch repository interfaces.
2025-07-30 22:07:53 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 22:07:53 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-30 22:07:53 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 1 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-30 22:07:53 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 22:07:53 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 22:07:53 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 0 ms. Found 0 Redis repository interfaces.
2025-07-30 22:07:54 [INFO][org.springframework.boot.web.embedded.tomcat.TomcatWebServer][initialize][111]-> Tomcat initialized with port 7070 (http)
2025-07-30 22:07:54 [INFO][org.apache.coyote.http11.Http11NioProtocol][log][173]-> Initializing ProtocolHandler ["http-nio-7070"]
2025-07-30 22:07:54 [INFO][org.apache.catalina.core.StandardService][log][173]-> Starting service [Tomcat]
2025-07-30 22:07:54 [INFO][org.apache.catalina.core.StandardEngine][log][173]-> Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-07-30 22:07:54 [INFO][org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/admin]][log][173]-> Initializing Spring embedded WebApplicationContext
2025-07-30 22:07:54 [INFO][org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext][prepareWebApplicationContext][301]-> Root WebApplicationContext: initialization completed in 1402 ms
2025-07-30 22:07:54 [INFO][com.alibaba.druid.spring.boot3.autoconfigure.DruidDataSourceAutoConfigure][dataSource][66]-> Init DruidDataSource
2025-07-30 22:07:54 [INFO][com.alibaba.druid.pool.DruidDataSource][init][1007]-> {dataSource-1} inited
2025-07-30 22:07:55 [INFO][org.apache.coyote.http11.Http11NioProtocol][log][173]-> Starting ProtocolHandler ["http-nio-7070"]
2025-07-30 22:07:55 [INFO][org.springframework.boot.web.embedded.tomcat.TomcatWebServer][start][243]-> Tomcat started on port 7070 (http) with context path '/admin'
2025-07-30 22:07:55 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStarted][59]-> Started BilibiliAdminApplication in 3.446 seconds (process running for 4.511)
2025-07-30 22:08:01 [INFO][org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/admin]][log][173]-> Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 22:08:01 [INFO][org.springframework.web.servlet.DispatcherServlet][initServletBean][532]-> Initializing Servlet 'dispatcherServlet'
2025-07-30 22:08:01 [INFO][org.springframework.web.servlet.DispatcherServlet][initServletBean][554]-> Completed initialization in 1 ms
2025-07-30 22:08:01 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 22:08:01 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 22:08:01 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 22:08:02 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/favicon.ico, 错误信息:
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:73)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-30 22:08:02 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 22:08:02 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/v3/api-docs/default, 错误信息:
jakarta.servlet.ServletException: Handler dispatch failed: java.lang.NoSuchMethodError: 'void org.springframework.web.method.ControllerAdviceBean.<init>(java.lang.Object)'
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1104)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NoSuchMethodError: 'void org.springframework.web.method.ControllerAdviceBean.<init>(java.lang.Object)'
	at org.springdoc.core.service.GenericResponseService.lambda$getGenericMapResponse$8(GenericResponseService.java:702)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:178)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:1024)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at org.springdoc.core.service.GenericResponseService.getGenericMapResponse(GenericResponseService.java:704)
	at org.springdoc.core.service.GenericResponseService.build(GenericResponseService.java:246)
	at org.springdoc.api.AbstractOpenApiResource.calculatePath(AbstractOpenApiResource.java:499)
	at org.springdoc.api.AbstractOpenApiResource.calculatePath(AbstractOpenApiResource.java:676)
	at org.springdoc.webmvc.api.OpenApiResource.lambda$calculatePath$11(OpenApiResource.java:219)
	at java.base/java.util.Optional.ifPresent(Optional.java:178)
	at org.springdoc.webmvc.api.OpenApiResource.calculatePath(OpenApiResource.java:200)
	at org.springdoc.webmvc.api.OpenApiResource.lambda$getPaths$2(OpenApiResource.java:170)
	at java.base/java.util.Optional.ifPresent(Optional.java:178)
	at org.springdoc.webmvc.api.OpenApiResource.getPaths(OpenApiResource.java:149)
	at org.springdoc.api.AbstractOpenApiResource.getOpenApi(AbstractOpenApiResource.java:353)
	at org.springdoc.webmvc.api.OpenApiResource.openapiJson(OpenApiResource.java:124)
	at org.springdoc.webmvc.api.OpenApiWebMvcResource.openapiJson(OpenApiWebMvcResource.java:111)
	at org.springdoc.webmvc.api.MultipleOpenApiWebMvcResource.openapiJson(MultipleOpenApiWebMvcResource.java:95)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	... 42 common frames omitted
2025-07-30 22:08:05 [INFO][com.bilibili.common.interceptor.RefreshTokenInterceptor][preHandle][33]-> RefreshTokenInterceptor preHandle executed
2025-07-30 22:08:05 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/favicon.ico, 错误信息:
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:73)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-30 22:09:34 [INFO][org.springframework.boot.web.embedded.tomcat.GracefulShutdown][shutDownGracefully][54]-> Commencing graceful shutdown. Waiting for active requests to complete
2025-07-30 22:09:34 [INFO][org.springframework.boot.web.embedded.tomcat.GracefulShutdown][doShutdown][76]-> Graceful shutdown complete
2025-07-30 22:09:34 [INFO][com.alibaba.druid.pool.DruidDataSource][close][2212]-> {dataSource-1} closing ...
2025-07-30 22:09:34 [INFO][com.alibaba.druid.pool.DruidDataSource][close][2285]-> {dataSource-1} closed
2025-07-30 22:09:39 [INFO][org.hibernate.validator.internal.util.Version][<clinit>][21]-> HV000001: Hibernate Validator 8.0.2.Final
2025-07-30 22:09:39 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStarting][53]-> Starting BilibiliAdminApplication using Java 21.0.7 with PID 24088 (D:\Workspace\java_code\bilibili-backend\bilibili-admin\target\classes started by 26788 in D:\Workspace\java_code\bilibili-backend)
2025-07-30 22:09:39 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStartupProfileInfo][652]-> No active profile set, falling back to 1 default profile: "default"
2025-07-30 22:09:40 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 22:09:40 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-30 22:09:40 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 9 ms. Found 0 Elasticsearch repository interfaces.
2025-07-30 22:09:40 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 22:09:40 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-30 22:09:40 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 0 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-30 22:09:40 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 22:09:40 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 22:09:40 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 0 ms. Found 0 Redis repository interfaces.
2025-07-30 22:09:40 [INFO][org.springframework.boot.web.embedded.tomcat.TomcatWebServer][initialize][111]-> Tomcat initialized with port 7070 (http)
2025-07-30 22:09:40 [INFO][org.apache.coyote.http11.Http11NioProtocol][log][173]-> Initializing ProtocolHandler ["http-nio-7070"]
2025-07-30 22:09:40 [INFO][org.apache.catalina.core.StandardService][log][173]-> Starting service [Tomcat]
2025-07-30 22:09:40 [INFO][org.apache.catalina.core.StandardEngine][log][173]-> Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-07-30 22:09:40 [INFO][org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/admin]][log][173]-> Initializing Spring embedded WebApplicationContext
2025-07-30 22:09:40 [INFO][org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext][prepareWebApplicationContext][301]-> Root WebApplicationContext: initialization completed in 1301 ms
2025-07-30 22:09:40 [INFO][com.alibaba.druid.spring.boot3.autoconfigure.DruidDataSourceAutoConfigure][dataSource][66]-> Init DruidDataSource
2025-07-30 22:09:40 [INFO][com.alibaba.druid.pool.DruidDataSource][init][1007]-> {dataSource-1} inited
2025-07-30 22:09:42 [INFO][org.apache.coyote.http11.Http11NioProtocol][log][173]-> Starting ProtocolHandler ["http-nio-7070"]
2025-07-30 22:09:42 [INFO][org.springframework.boot.web.embedded.tomcat.TomcatWebServer][start][243]-> Tomcat started on port 7070 (http) with context path '/admin'
2025-07-30 22:09:42 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStarted][59]-> Started BilibiliAdminApplication in 3.404 seconds (process running for 4.203)
2025-07-30 22:09:45 [INFO][org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/admin]][log][173]-> Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 22:09:45 [INFO][org.springframework.web.servlet.DispatcherServlet][initServletBean][532]-> Initializing Servlet 'dispatcherServlet'
2025-07-30 22:09:45 [INFO][org.springframework.web.servlet.DispatcherServlet][initServletBean][554]-> Completed initialization in 1 ms
2025-07-30 22:09:45 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/favicon.ico, 错误信息:
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:73)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-30 22:09:45 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/v3/api-docs/default, 错误信息:
jakarta.servlet.ServletException: Handler dispatch failed: java.lang.NoSuchMethodError: 'void org.springframework.web.method.ControllerAdviceBean.<init>(java.lang.Object)'
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1104)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NoSuchMethodError: 'void org.springframework.web.method.ControllerAdviceBean.<init>(java.lang.Object)'
	at org.springdoc.core.service.GenericResponseService.lambda$getGenericMapResponse$8(GenericResponseService.java:702)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:178)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:1024)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at org.springdoc.core.service.GenericResponseService.getGenericMapResponse(GenericResponseService.java:704)
	at org.springdoc.core.service.GenericResponseService.build(GenericResponseService.java:246)
	at org.springdoc.api.AbstractOpenApiResource.calculatePath(AbstractOpenApiResource.java:499)
	at org.springdoc.api.AbstractOpenApiResource.calculatePath(AbstractOpenApiResource.java:676)
	at org.springdoc.webmvc.api.OpenApiResource.lambda$calculatePath$11(OpenApiResource.java:219)
	at java.base/java.util.Optional.ifPresent(Optional.java:178)
	at org.springdoc.webmvc.api.OpenApiResource.calculatePath(OpenApiResource.java:200)
	at org.springdoc.webmvc.api.OpenApiResource.lambda$getPaths$2(OpenApiResource.java:170)
	at java.base/java.util.Optional.ifPresent(Optional.java:178)
	at org.springdoc.webmvc.api.OpenApiResource.getPaths(OpenApiResource.java:149)
	at org.springdoc.api.AbstractOpenApiResource.getOpenApi(AbstractOpenApiResource.java:353)
	at org.springdoc.webmvc.api.OpenApiResource.openapiJson(OpenApiResource.java:124)
	at org.springdoc.webmvc.api.OpenApiWebMvcResource.openapiJson(OpenApiWebMvcResource.java:111)
	at org.springdoc.webmvc.api.MultipleOpenApiWebMvcResource.openapiJson(MultipleOpenApiWebMvcResource.java:95)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	... 42 common frames omitted
2025-07-30 22:12:46 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/favicon.ico, 错误信息:
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:73)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-30 22:12:46 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/v3/api-docs/default, 错误信息:
jakarta.servlet.ServletException: Handler dispatch failed: java.lang.NoSuchMethodError: 'void org.springframework.web.method.ControllerAdviceBean.<init>(java.lang.Object)'
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1104)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NoSuchMethodError: 'void org.springframework.web.method.ControllerAdviceBean.<init>(java.lang.Object)'
	at org.springdoc.core.service.GenericResponseService.lambda$getGenericMapResponse$8(GenericResponseService.java:702)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:178)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:1024)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at org.springdoc.core.service.GenericResponseService.getGenericMapResponse(GenericResponseService.java:704)
	at org.springdoc.core.service.GenericResponseService.build(GenericResponseService.java:246)
	at org.springdoc.api.AbstractOpenApiResource.calculatePath(AbstractOpenApiResource.java:499)
	at org.springdoc.api.AbstractOpenApiResource.calculatePath(AbstractOpenApiResource.java:676)
	at org.springdoc.webmvc.api.OpenApiResource.lambda$calculatePath$11(OpenApiResource.java:219)
	at java.base/java.util.Optional.ifPresent(Optional.java:178)
	at org.springdoc.webmvc.api.OpenApiResource.calculatePath(OpenApiResource.java:200)
	at org.springdoc.webmvc.api.OpenApiResource.lambda$getPaths$2(OpenApiResource.java:170)
	at java.base/java.util.Optional.ifPresent(Optional.java:178)
	at org.springdoc.webmvc.api.OpenApiResource.getPaths(OpenApiResource.java:149)
	at org.springdoc.api.AbstractOpenApiResource.getOpenApi(AbstractOpenApiResource.java:353)
	at org.springdoc.webmvc.api.OpenApiResource.openapiJson(OpenApiResource.java:124)
	at org.springdoc.webmvc.api.OpenApiWebMvcResource.openapiJson(OpenApiWebMvcResource.java:111)
	at org.springdoc.webmvc.api.MultipleOpenApiWebMvcResource.openapiJson(MultipleOpenApiWebMvcResource.java:95)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	... 42 common frames omitted
2025-07-30 22:19:05 [INFO][org.springframework.boot.web.embedded.tomcat.GracefulShutdown][shutDownGracefully][54]-> Commencing graceful shutdown. Waiting for active requests to complete
2025-07-30 22:19:05 [INFO][org.springframework.boot.web.embedded.tomcat.GracefulShutdown][doShutdown][76]-> Graceful shutdown complete
2025-07-30 22:19:05 [INFO][com.alibaba.druid.pool.DruidDataSource][close][2212]-> {dataSource-1} closing ...
2025-07-30 22:19:05 [INFO][com.alibaba.druid.pool.DruidDataSource][close][2285]-> {dataSource-1} closed
2025-07-30 22:19:13 [INFO][org.hibernate.validator.internal.util.Version][<clinit>][21]-> HV000001: Hibernate Validator 8.0.2.Final
2025-07-30 22:19:13 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStarting][53]-> Starting BilibiliAdminApplication using Java 21.0.7 with PID 15444 (D:\Workspace\java_code\bilibili-backend\bilibili-admin\target\classes started by 26788 in D:\Workspace\java_code\bilibili-backend)
2025-07-30 22:19:13 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStartupProfileInfo][652]-> No active profile set, falling back to 1 default profile: "default"
2025-07-30 22:19:14 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 22:19:14 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-30 22:19:14 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 10 ms. Found 0 Elasticsearch repository interfaces.
2025-07-30 22:19:14 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 22:19:14 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-30 22:19:14 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 0 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-30 22:19:14 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 22:19:14 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 22:19:14 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 0 ms. Found 0 Redis repository interfaces.
2025-07-30 22:19:15 [INFO][org.springframework.boot.web.embedded.tomcat.TomcatWebServer][initialize][111]-> Tomcat initialized with port 7070 (http)
2025-07-30 22:19:15 [INFO][org.apache.coyote.http11.Http11NioProtocol][log][173]-> Initializing ProtocolHandler ["http-nio-7070"]
2025-07-30 22:19:15 [INFO][org.apache.catalina.core.StandardService][log][173]-> Starting service [Tomcat]
2025-07-30 22:19:15 [INFO][org.apache.catalina.core.StandardEngine][log][173]-> Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-07-30 22:19:15 [INFO][org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/admin]][log][173]-> Initializing Spring embedded WebApplicationContext
2025-07-30 22:19:15 [INFO][org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext][prepareWebApplicationContext][301]-> Root WebApplicationContext: initialization completed in 1773 ms
2025-07-30 22:19:15 [INFO][com.alibaba.druid.spring.boot3.autoconfigure.DruidDataSourceAutoConfigure][dataSource][66]-> Init DruidDataSource
2025-07-30 22:19:15 [INFO][com.alibaba.druid.pool.DruidDataSource][init][1007]-> {dataSource-1} inited
2025-07-30 22:19:17 [INFO][org.apache.coyote.http11.Http11NioProtocol][log][173]-> Starting ProtocolHandler ["http-nio-7070"]
2025-07-30 22:19:17 [INFO][org.springframework.boot.web.embedded.tomcat.TomcatWebServer][start][243]-> Tomcat started on port 7070 (http) with context path '/admin'
2025-07-30 22:19:17 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStarted][59]-> Started BilibiliAdminApplication in 4.313 seconds (process running for 5.435)
2025-07-30 22:19:17 [INFO][org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/admin]][log][173]-> Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 22:19:17 [INFO][org.springframework.web.servlet.DispatcherServlet][initServletBean][532]-> Initializing Servlet 'dispatcherServlet'
2025-07-30 22:19:17 [INFO][org.springframework.web.servlet.DispatcherServlet][initServletBean][554]-> Completed initialization in 1 ms
2025-07-30 22:19:17 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/favicon.ico, 错误信息:
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:73)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-30 22:19:17 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/v3/api-docs/default, 错误信息:
jakarta.servlet.ServletException: Handler dispatch failed: java.lang.NoSuchMethodError: 'void org.springframework.web.method.ControllerAdviceBean.<init>(java.lang.Object)'
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1104)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NoSuchMethodError: 'void org.springframework.web.method.ControllerAdviceBean.<init>(java.lang.Object)'
	at org.springdoc.core.service.GenericResponseService.lambda$getGenericMapResponse$8(GenericResponseService.java:702)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:178)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:1024)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at org.springdoc.core.service.GenericResponseService.getGenericMapResponse(GenericResponseService.java:704)
	at org.springdoc.core.service.GenericResponseService.build(GenericResponseService.java:246)
	at org.springdoc.api.AbstractOpenApiResource.calculatePath(AbstractOpenApiResource.java:499)
	at org.springdoc.api.AbstractOpenApiResource.calculatePath(AbstractOpenApiResource.java:676)
	at org.springdoc.webmvc.api.OpenApiResource.lambda$calculatePath$11(OpenApiResource.java:219)
	at java.base/java.util.Optional.ifPresent(Optional.java:178)
	at org.springdoc.webmvc.api.OpenApiResource.calculatePath(OpenApiResource.java:200)
	at org.springdoc.webmvc.api.OpenApiResource.lambda$getPaths$2(OpenApiResource.java:170)
	at java.base/java.util.Optional.ifPresent(Optional.java:178)
	at org.springdoc.webmvc.api.OpenApiResource.getPaths(OpenApiResource.java:149)
	at org.springdoc.api.AbstractOpenApiResource.getOpenApi(AbstractOpenApiResource.java:353)
	at org.springdoc.webmvc.api.OpenApiResource.openapiJson(OpenApiResource.java:124)
	at org.springdoc.webmvc.api.OpenApiWebMvcResource.openapiJson(OpenApiWebMvcResource.java:111)
	at org.springdoc.webmvc.api.MultipleOpenApiWebMvcResource.openapiJson(MultipleOpenApiWebMvcResource.java:95)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	... 42 common frames omitted
2025-07-30 22:21:20 [INFO][org.springframework.boot.web.embedded.tomcat.GracefulShutdown][shutDownGracefully][54]-> Commencing graceful shutdown. Waiting for active requests to complete
2025-07-30 22:21:20 [INFO][org.springframework.boot.web.embedded.tomcat.GracefulShutdown][doShutdown][76]-> Graceful shutdown complete
2025-07-30 22:21:20 [INFO][com.alibaba.druid.pool.DruidDataSource][close][2212]-> {dataSource-1} closing ...
2025-07-30 22:21:20 [INFO][com.alibaba.druid.pool.DruidDataSource][close][2285]-> {dataSource-1} closed
2025-07-30 22:23:19 [INFO][org.hibernate.validator.internal.util.Version][<clinit>][21]-> HV000001: Hibernate Validator 8.0.2.Final
2025-07-30 22:23:19 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStarting][53]-> Starting BilibiliAdminApplication using Java 21.0.7 with PID 15840 (D:\Workspace\java_code\bilibili-backend\bilibili-admin\target\classes started by 26788 in D:\Workspace\java_code\bilibili-backend)
2025-07-30 22:23:19 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStartupProfileInfo][652]-> No active profile set, falling back to 1 default profile: "default"
2025-07-30 22:23:20 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 22:23:20 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-30 22:23:20 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 12 ms. Found 0 Elasticsearch repository interfaces.
2025-07-30 22:23:20 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 22:23:20 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-30 22:23:20 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 1 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-30 22:23:20 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 22:23:20 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 22:23:20 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 1 ms. Found 0 Redis repository interfaces.
2025-07-30 22:23:20 [INFO][org.springframework.boot.web.embedded.tomcat.TomcatWebServer][initialize][111]-> Tomcat initialized with port 7070 (http)
2025-07-30 22:23:20 [INFO][org.apache.coyote.http11.Http11NioProtocol][log][173]-> Initializing ProtocolHandler ["http-nio-7070"]
2025-07-30 22:23:20 [INFO][org.apache.catalina.core.StandardService][log][173]-> Starting service [Tomcat]
2025-07-30 22:23:20 [INFO][org.apache.catalina.core.StandardEngine][log][173]-> Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-07-30 22:23:20 [INFO][org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/admin]][log][173]-> Initializing Spring embedded WebApplicationContext
2025-07-30 22:23:20 [INFO][org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext][prepareWebApplicationContext][301]-> Root WebApplicationContext: initialization completed in 1341 ms
2025-07-30 22:23:20 [INFO][com.alibaba.druid.spring.boot3.autoconfigure.DruidDataSourceAutoConfigure][dataSource][66]-> Init DruidDataSource
2025-07-30 22:23:21 [INFO][com.alibaba.druid.pool.DruidDataSource][init][1007]-> {dataSource-1} inited
2025-07-30 22:23:22 [INFO][org.apache.coyote.http11.Http11NioProtocol][log][173]-> Starting ProtocolHandler ["http-nio-7070"]
2025-07-30 22:23:22 [INFO][org.springframework.boot.web.embedded.tomcat.TomcatWebServer][start][243]-> Tomcat started on port 7070 (http) with context path '/admin'
2025-07-30 22:23:22 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStarted][59]-> Started BilibiliAdminApplication in 3.822 seconds (process running for 4.781)
2025-07-30 22:23:23 [INFO][org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/admin]][log][173]-> Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 22:23:23 [INFO][org.springframework.web.servlet.DispatcherServlet][initServletBean][532]-> Initializing Servlet 'dispatcherServlet'
2025-07-30 22:23:23 [INFO][org.springframework.web.servlet.DispatcherServlet][initServletBean][554]-> Completed initialization in 2 ms
2025-07-30 22:23:24 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/favicon.ico, 错误信息:
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:73)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-30 22:23:24 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/v3/api-docs/default, 错误信息:
jakarta.servlet.ServletException: Handler dispatch failed: java.lang.NoSuchMethodError: 'void org.springframework.web.method.ControllerAdviceBean.<init>(java.lang.Object)'
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1104)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NoSuchMethodError: 'void org.springframework.web.method.ControllerAdviceBean.<init>(java.lang.Object)'
	at org.springdoc.core.service.GenericResponseService.lambda$getGenericMapResponse$8(GenericResponseService.java:702)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:178)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:1024)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at org.springdoc.core.service.GenericResponseService.getGenericMapResponse(GenericResponseService.java:704)
	at org.springdoc.core.service.GenericResponseService.build(GenericResponseService.java:246)
	at org.springdoc.api.AbstractOpenApiResource.calculatePath(AbstractOpenApiResource.java:499)
	at org.springdoc.api.AbstractOpenApiResource.calculatePath(AbstractOpenApiResource.java:676)
	at org.springdoc.webmvc.api.OpenApiResource.lambda$calculatePath$11(OpenApiResource.java:219)
	at java.base/java.util.Optional.ifPresent(Optional.java:178)
	at org.springdoc.webmvc.api.OpenApiResource.calculatePath(OpenApiResource.java:200)
	at org.springdoc.webmvc.api.OpenApiResource.lambda$getPaths$2(OpenApiResource.java:170)
	at java.base/java.util.Optional.ifPresent(Optional.java:178)
	at org.springdoc.webmvc.api.OpenApiResource.getPaths(OpenApiResource.java:149)
	at org.springdoc.api.AbstractOpenApiResource.getOpenApi(AbstractOpenApiResource.java:353)
	at org.springdoc.webmvc.api.OpenApiResource.openapiJson(OpenApiResource.java:124)
	at org.springdoc.webmvc.api.OpenApiWebMvcResource.openapiJson(OpenApiWebMvcResource.java:111)
	at org.springdoc.webmvc.api.MultipleOpenApiWebMvcResource.openapiJson(MultipleOpenApiWebMvcResource.java:95)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	... 42 common frames omitted
2025-07-30 22:26:51 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/favicon.ico, 错误信息:
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:73)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-30 22:26:51 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/v3/api-docs/default, 错误信息:
jakarta.servlet.ServletException: Handler dispatch failed: java.lang.NoSuchMethodError: 'void org.springframework.web.method.ControllerAdviceBean.<init>(java.lang.Object)'
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1104)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NoSuchMethodError: 'void org.springframework.web.method.ControllerAdviceBean.<init>(java.lang.Object)'
	at org.springdoc.core.service.GenericResponseService.lambda$getGenericMapResponse$8(GenericResponseService.java:702)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:178)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:1024)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at org.springdoc.core.service.GenericResponseService.getGenericMapResponse(GenericResponseService.java:704)
	at org.springdoc.core.service.GenericResponseService.build(GenericResponseService.java:246)
	at org.springdoc.api.AbstractOpenApiResource.calculatePath(AbstractOpenApiResource.java:499)
	at org.springdoc.api.AbstractOpenApiResource.calculatePath(AbstractOpenApiResource.java:676)
	at org.springdoc.webmvc.api.OpenApiResource.lambda$calculatePath$11(OpenApiResource.java:219)
	at java.base/java.util.Optional.ifPresent(Optional.java:178)
	at org.springdoc.webmvc.api.OpenApiResource.calculatePath(OpenApiResource.java:200)
	at org.springdoc.webmvc.api.OpenApiResource.lambda$getPaths$2(OpenApiResource.java:170)
	at java.base/java.util.Optional.ifPresent(Optional.java:178)
	at org.springdoc.webmvc.api.OpenApiResource.getPaths(OpenApiResource.java:149)
	at org.springdoc.api.AbstractOpenApiResource.getOpenApi(AbstractOpenApiResource.java:353)
	at org.springdoc.webmvc.api.OpenApiResource.openapiJson(OpenApiResource.java:124)
	at org.springdoc.webmvc.api.OpenApiWebMvcResource.openapiJson(OpenApiWebMvcResource.java:111)
	at org.springdoc.webmvc.api.MultipleOpenApiWebMvcResource.openapiJson(MultipleOpenApiWebMvcResource.java:95)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	... 42 common frames omitted
2025-07-30 22:37:31 [INFO][org.hibernate.validator.internal.util.Version][<clinit>][21]-> HV000001: Hibernate Validator 8.0.2.Final
2025-07-30 22:37:31 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStarting][53]-> Starting BilibiliAdminApplication using Java 21.0.7 with PID 20568 (D:\Workspace\java_code\bilibili-backend\bilibili-admin\target\classes started by 26788 in D:\Workspace\java_code\bilibili-backend)
2025-07-30 22:37:31 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStartupProfileInfo][652]-> No active profile set, falling back to 1 default profile: "default"
2025-07-30 22:37:32 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 22:37:32 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-30 22:37:32 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 15 ms. Found 0 Elasticsearch repository interfaces.
2025-07-30 22:37:32 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 22:37:32 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-30 22:37:32 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 0 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-30 22:37:32 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 22:37:32 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 22:37:32 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 0 ms. Found 0 Redis repository interfaces.
2025-07-30 22:37:32 [INFO][org.springframework.boot.web.embedded.tomcat.TomcatWebServer][initialize][111]-> Tomcat initialized with port 7070 (http)
2025-07-30 22:37:32 [INFO][org.apache.coyote.http11.Http11NioProtocol][log][173]-> Initializing ProtocolHandler ["http-nio-7070"]
2025-07-30 22:37:32 [INFO][org.apache.catalina.core.StandardService][log][173]-> Starting service [Tomcat]
2025-07-30 22:37:32 [INFO][org.apache.catalina.core.StandardEngine][log][173]-> Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-07-30 22:37:32 [INFO][org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/admin]][log][173]-> Initializing Spring embedded WebApplicationContext
2025-07-30 22:37:32 [INFO][org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext][prepareWebApplicationContext][301]-> Root WebApplicationContext: initialization completed in 1418 ms
2025-07-30 22:37:32 [INFO][com.alibaba.druid.spring.boot3.autoconfigure.DruidDataSourceAutoConfigure][dataSource][66]-> Init DruidDataSource
2025-07-30 22:37:33 [INFO][com.alibaba.druid.pool.DruidDataSource][init][1007]-> {dataSource-1} inited
2025-07-30 22:37:35 [INFO][org.apache.coyote.http11.Http11NioProtocol][log][173]-> Starting ProtocolHandler ["http-nio-7070"]
2025-07-30 22:37:35 [INFO][org.springframework.boot.web.embedded.tomcat.TomcatWebServer][start][243]-> Tomcat started on port 7070 (http) with context path '/admin'
2025-07-30 22:37:35 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStarted][59]-> Started BilibiliAdminApplication in 4.243 seconds (process running for 8.142)
2025-07-30 22:37:35 [INFO][org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/admin]][log][173]-> Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 22:37:35 [INFO][org.springframework.web.servlet.DispatcherServlet][initServletBean][532]-> Initializing Servlet 'dispatcherServlet'
2025-07-30 22:37:35 [INFO][org.springframework.web.servlet.DispatcherServlet][initServletBean][554]-> Completed initialization in 1 ms
2025-07-30 22:37:36 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/v3/api-docs/default, 错误信息:
jakarta.servlet.ServletException: Handler dispatch failed: java.lang.NoSuchMethodError: 'void org.springframework.web.method.ControllerAdviceBean.<init>(java.lang.Object)'
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1104)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NoSuchMethodError: 'void org.springframework.web.method.ControllerAdviceBean.<init>(java.lang.Object)'
	at org.springdoc.core.service.GenericResponseService.lambda$getGenericMapResponse$8(GenericResponseService.java:706)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:178)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:1024)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at org.springdoc.core.service.GenericResponseService.getGenericMapResponse(GenericResponseService.java:708)
	at org.springdoc.core.service.GenericResponseService.build(GenericResponseService.java:247)
	at org.springdoc.api.AbstractOpenApiResource.calculatePath(AbstractOpenApiResource.java:499)
	at org.springdoc.api.AbstractOpenApiResource.calculatePath(AbstractOpenApiResource.java:676)
	at org.springdoc.webmvc.api.OpenApiResource.lambda$calculatePath$11(OpenApiResource.java:220)
	at java.base/java.util.Optional.ifPresent(Optional.java:178)
	at org.springdoc.webmvc.api.OpenApiResource.calculatePath(OpenApiResource.java:201)
	at org.springdoc.webmvc.api.OpenApiResource.lambda$getPaths$2(OpenApiResource.java:171)
	at java.base/java.util.Optional.ifPresent(Optional.java:178)
	at org.springdoc.webmvc.api.OpenApiResource.getPaths(OpenApiResource.java:150)
	at org.springdoc.api.AbstractOpenApiResource.getOpenApi(AbstractOpenApiResource.java:353)
	at org.springdoc.webmvc.api.OpenApiResource.openapiJson(OpenApiResource.java:125)
	at org.springdoc.webmvc.api.OpenApiWebMvcResource.openapiJson(OpenApiWebMvcResource.java:114)
	at org.springdoc.webmvc.api.MultipleOpenApiWebMvcResource.openapiJson(MultipleOpenApiWebMvcResource.java:95)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	... 42 common frames omitted
2025-07-30 22:41:04 [INFO][org.springframework.boot.web.embedded.tomcat.GracefulShutdown][shutDownGracefully][54]-> Commencing graceful shutdown. Waiting for active requests to complete
2025-07-30 22:41:04 [INFO][org.springframework.boot.web.embedded.tomcat.GracefulShutdown][doShutdown][76]-> Graceful shutdown complete
2025-07-30 22:41:04 [INFO][com.alibaba.druid.pool.DruidDataSource][close][2212]-> {dataSource-1} closing ...
2025-07-30 22:41:04 [INFO][com.alibaba.druid.pool.DruidDataSource][close][2285]-> {dataSource-1} closed
2025-07-30 22:41:11 [INFO][org.hibernate.validator.internal.util.Version][<clinit>][21]-> HV000001: Hibernate Validator 8.0.2.Final
2025-07-30 22:41:11 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStarting][53]-> Starting BilibiliAdminApplication using Java 21.0.7 with PID 6828 (D:\Workspace\java_code\bilibili-backend\bilibili-admin\target\classes started by 26788 in D:\Workspace\java_code\bilibili-backend)
2025-07-30 22:41:11 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStartupProfileInfo][652]-> No active profile set, falling back to 1 default profile: "default"
2025-07-30 22:41:12 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 22:41:12 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-30 22:41:12 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 15 ms. Found 0 Elasticsearch repository interfaces.
2025-07-30 22:41:12 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 22:41:12 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-30 22:41:12 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 0 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-30 22:41:12 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][multipleStoresDetected][295]-> Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 22:41:12 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][143]-> Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 22:41:12 [INFO][org.springframework.data.repository.config.RepositoryConfigurationDelegate][registerRepositoriesIn][211]-> Finished Spring Data repository scanning in 1 ms. Found 0 Redis repository interfaces.
2025-07-30 22:41:12 [INFO][org.springframework.boot.web.embedded.tomcat.TomcatWebServer][initialize][111]-> Tomcat initialized with port 7070 (http)
2025-07-30 22:41:12 [INFO][org.apache.coyote.http11.Http11NioProtocol][log][173]-> Initializing ProtocolHandler ["http-nio-7070"]
2025-07-30 22:41:12 [INFO][org.apache.catalina.core.StandardService][log][173]-> Starting service [Tomcat]
2025-07-30 22:41:12 [INFO][org.apache.catalina.core.StandardEngine][log][173]-> Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-07-30 22:41:12 [INFO][org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/admin]][log][173]-> Initializing Spring embedded WebApplicationContext
2025-07-30 22:41:12 [INFO][org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext][prepareWebApplicationContext][301]-> Root WebApplicationContext: initialization completed in 1462 ms
2025-07-30 22:41:13 [INFO][com.alibaba.druid.spring.boot3.autoconfigure.DruidDataSourceAutoConfigure][dataSource][66]-> Init DruidDataSource
2025-07-30 22:41:13 [INFO][com.alibaba.druid.pool.DruidDataSource][init][1007]-> {dataSource-1} inited
2025-07-30 22:41:14 [INFO][org.apache.coyote.http11.Http11NioProtocol][log][173]-> Starting ProtocolHandler ["http-nio-7070"]
2025-07-30 22:41:14 [INFO][org.springframework.boot.web.embedded.tomcat.TomcatWebServer][start][243]-> Tomcat started on port 7070 (http) with context path '/admin'
2025-07-30 22:41:14 [INFO][com.bilibili.admin.BilibiliAdminApplication][logStarted][59]-> Started BilibiliAdminApplication in 3.872 seconds (process running for 4.839)
2025-07-30 22:41:15 [INFO][org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/admin]][log][173]-> Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 22:41:15 [INFO][org.springframework.web.servlet.DispatcherServlet][initServletBean][532]-> Initializing Servlet 'dispatcherServlet'
2025-07-30 22:41:15 [INFO][org.springframework.web.servlet.DispatcherServlet][initServletBean][554]-> Completed initialization in 1 ms
2025-07-30 22:41:16 [ERROR][com.bilibili.common.exception.GlobalExceptionHandler][handleException][62]-> 系统异常，请求地址: http://localhost:7070/admin/v3/api-docs/default, 错误信息:
jakarta.servlet.ServletException: Handler dispatch failed: java.lang.NoSuchMethodError: 'void org.springframework.web.method.ControllerAdviceBean.<init>(java.lang.Object)'
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1104)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NoSuchMethodError: 'void org.springframework.web.method.ControllerAdviceBean.<init>(java.lang.Object)'
	at org.springdoc.core.service.GenericResponseService.lambda$getGenericMapResponse$8(GenericResponseService.java:706)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:178)
	at java.base/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:1024)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at org.springdoc.core.service.GenericResponseService.getGenericMapResponse(GenericResponseService.java:708)
	at org.springdoc.core.service.GenericResponseService.build(GenericResponseService.java:247)
	at org.springdoc.api.AbstractOpenApiResource.calculatePath(AbstractOpenApiResource.java:499)
	at org.springdoc.api.AbstractOpenApiResource.calculatePath(AbstractOpenApiResource.java:676)
	at org.springdoc.webmvc.api.OpenApiResource.lambda$calculatePath$11(OpenApiResource.java:220)
	at java.base/java.util.Optional.ifPresent(Optional.java:178)
	at org.springdoc.webmvc.api.OpenApiResource.calculatePath(OpenApiResource.java:201)
	at org.springdoc.webmvc.api.OpenApiResource.lambda$getPaths$2(OpenApiResource.java:171)
	at java.base/java.util.Optional.ifPresent(Optional.java:178)
	at org.springdoc.webmvc.api.OpenApiResource.getPaths(OpenApiResource.java:150)
	at org.springdoc.api.AbstractOpenApiResource.getOpenApi(AbstractOpenApiResource.java:353)
	at org.springdoc.webmvc.api.OpenApiResource.openapiJson(OpenApiResource.java:125)
	at org.springdoc.webmvc.api.OpenApiWebMvcResource.openapiJson(OpenApiWebMvcResource.java:114)
	at org.springdoc.webmvc.api.MultipleOpenApiWebMvcResource.openapiJson(MultipleOpenApiWebMvcResource.java:95)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	... 42 common frames omitted
