package com.bilibili.common.model.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;


@Getter
@Setter
public class UserVO implements Serializable {

    private static final long serialVersionUID = 8412508359473002786L;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 头像
     */
    private String avatar;

    /**
     * token过期时间（毫秒）
     */
    private Long expireAt;

    /**
     * token
     */
    private String token;

    /**
     * 粉丝数
     */
    private Integer fansCount;

    /**
     * 关注数
     */
    private Integer focusCount;

    /**
     * 当前硬币数
     */
    private Integer currentCoinCount;

}
