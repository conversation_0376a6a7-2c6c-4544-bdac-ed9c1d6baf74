package com.bilibili.common.model.dto.web.account;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 登录请求
 */
@Data
public class LoginRequest {
    /**
     * 邮箱
     */
    @NotEmpty(message = "邮箱不能为空")
    @Email(regexp = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", message = "邮箱格式不正确")
    @Size(max = 150, message = "邮箱最大长度为150个字符")
    private String email;

    /**
     * 密码
     */
    @NotEmpty(message = "密码不能为空")
    private String password;

    /**
     * 验证码
     */
    @NotEmpty(message = "验证码不能为空")
    private String checkCode;

    /**
     * 验证码key
     */
    @NotEmpty(message = "验证码key不能为空")
    private String checkCodeKey;
}
