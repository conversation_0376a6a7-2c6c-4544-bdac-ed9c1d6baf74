package com.bilibili.common.model.vo;

import lombok.Data;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
public class LoadCategoryResponse implements Serializable {

    /**
     * 分类ID
     */
    private Integer categoryId;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 分类编号
     */
    private String categoryCode;

    /**
     * 父分类ID
     */
    private Integer categoryPid;

    /**
     * 图标路径
     */
    private String icon;

    /**
     * 背景图路径
     */
    private String background;

    /**
     * 子分类
     */
    private List<LoadCategoryResponse> children;

    @Serial
    private static final long serialVersionUID = 641457484568989470L;

}
