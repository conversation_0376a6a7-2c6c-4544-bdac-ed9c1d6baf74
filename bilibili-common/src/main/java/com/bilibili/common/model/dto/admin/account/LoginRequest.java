package com.bilibili.common.model.dto.admin.account;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 登录请求
 */
@Data
public class LoginRequest {

    /**
     * 账号
     */
    @NotEmpty(message = "账号不能为空")
    @Size(max = 20, message = "账号最大长度为20个字符")
    private String account;

    /**
     * 密码
     */
    @NotEmpty(message = "密码不能为空")
    private String password;

    /**
     * 验证码
     */
    @NotEmpty(message = "验证码不能为空")
    private String checkCode;

    /**
     * 验证码key
     */
    @NotEmpty(message = "验证码key不能为空")
    private String checkCodeKey;
}
