package com.bilibili.common.model.dto.admin.category;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.NonNull;

@Data
public class SaveCateGoryRequest {

    /**
     * 分类ID（修改时传，新增时不传）
     */
    private Integer categoryId;

    /**
     * 分类名称
     */
    @NotBlank
    private String categoryName;

    /**
     * 分类编号
     */
    @NotBlank
    private String categoryCode;

    /**
     * 父分类ID（一级分类为0，二级分类为父分类ID）
     */
    @NonNull
    private Integer categoryPid;

    /**
     * 图标文件路径（仅一级分类）
     */
    private String icon;

    /**
     * 背景图文件路径（仅一级分类）
     */
    private String background;
}
