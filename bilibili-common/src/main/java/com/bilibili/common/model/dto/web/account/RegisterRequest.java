package com.bilibili.common.model.dto.web.account;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 注册请求
 */
@Data
public class RegisterRequest {

    /**
     * 邮箱
     */
    @NotEmpty(message = "邮箱不能为空")
    @Email(regexp = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", message = "邮箱格式不正确")
    @Size(max = 150, message = "邮箱最大长度为150个字符")
    private String email;

    /**
     * 昵称
     */
    @NotEmpty(message = "昵称不能为空")
    @Size(max = 20, message = "昵称最大长度为20个字符")
    private String nickName;

    /**
     * 注册时密码
     */
    @NotEmpty
    private String registerPassword;

    /**
     * 注册时再次确认密码
     */
    @NotEmpty
    private String reRegisterPassword;

    /**
     * 验证码
     */
    @NotEmpty
    private String checkCode;

    /**
     * 验证码key
     */
    @NotEmpty
    private String checkCodeKey;
}
