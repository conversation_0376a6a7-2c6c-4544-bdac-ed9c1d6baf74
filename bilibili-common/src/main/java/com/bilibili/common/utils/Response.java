package com.bilibili.common.utils;

import com.bilibili.common.model.enums.ResponseCodeEnum;
import com.bilibili.common.exception.BusinessException;
import lombok.Data;

import java.io.Serializable;

@Data
public class Response<T> implements Serializable {


    /**
     * 响应状态：success / error
     */
    private String status;

    /**
     * 响应状态码
     */
    private Integer code;
    /**
     * 响应信息
     */
    private String info;
    /**
     * 响应数据
     */
    private T data;

    private static final String STATUS_SUCCESS = "success";

    private static final String STATUS_ERROR = "error";


    // =================================== 成功响应 ===================================
    public static <T> Response<T> success() {
        Response<T> response = new Response<>();
        response.setStatus(STATUS_SUCCESS);
        response.setCode(ResponseCodeEnum.CODE_200.getCode());
        response.setInfo(ResponseCodeEnum.CODE_200.getMsg());
        return response;
    }

    public static <T> Response<T> success(T data) {
        Response<T> response = new Response<>();
        response.setStatus(STATUS_SUCCESS);
        response.setCode(ResponseCodeEnum.CODE_200.getCode());
        response.setInfo(ResponseCodeEnum.CODE_200.getMsg());
        response.setData(data);
        return response;
    }

    // =================================== 失败响应（全局异常处理器中使用） ===================================
    public static <T> Response<T> fail(BusinessException businessException) {
        Response<T> response = new Response<>();
        response.setStatus(STATUS_ERROR);
        response.setCode(businessException.getCode());
        response.setInfo(businessException.getMessage());
        return response;
    }

    public static <T> Response<T> fail(Integer code, String message) {
        Response<T> response = new Response<>();
        response.setStatus(STATUS_ERROR);
        response.setCode(code);
        response.setInfo(message);
        return response;
    }
}
