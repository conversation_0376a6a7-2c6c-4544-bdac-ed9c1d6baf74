package com.bilibili.common.utils;

import cn.hutool.core.util.RuntimeUtil;
import com.bilibili.common.config.app.AppConfig;
import jakarta.annotation.Resource;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Component;

@Component
public class FFmpegUtil {

    @Resource
    private AppConfig appConfig;

    public void createImageThumbnail(String sourcePath, String targetPath) {
        String CMD = "ffmpeg -i {sourcePath} -vf scale=200:-1 {targetPath}";

    }
}
