package com.bilibili.common.mapper;

import com.bilibili.common.model.entity.Category;
import com.bilibili.common.model.vo.LoadCategoryResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 分类信息表 数据库操作接口
 */
public interface CategoryMapper {

    List<LoadCategoryResponse> selectCategoryList();

    Category selectCategoryByCategoryCode(@Param("categoryCode") String categoryCode);

    Integer selectMaxSort(@Param("categoryPid") Integer categoryPid);

    void insert(@Param("category") Category category);

    void updateByCategoryId(@Param("category") Category category,@Param("categoryId") Integer categoryId);

    void deleteByCategoryId(@Param("categoryId") Integer categoryId);

    void batchUpdateCategorySort(@Param("updateCategoryList") List<Category> updateCategoryList);
}
