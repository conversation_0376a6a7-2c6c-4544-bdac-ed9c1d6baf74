package com.bilibili.common.interceptor;

import cn.hutool.core.util.StrUtil;
import com.bilibili.common.component.RedisComponent;
import com.bilibili.common.exception.BusinessException;
import com.bilibili.common.model.enums.ResponseCodeEnum;
import com.bilibili.common.utils.ServletUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import static com.bilibili.common.constants.CookieConstant.ADMIN_COOKIE_NAME_TOKEN;

/**
 * 后台登录状态检测拦截器
 */
@Component
public class LoginInterceptor implements HandlerInterceptor {

    @Resource
    private RedisComponent redisComponent;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 账号相关接口不拦截
        if (request.getRequestURI().contains("account")) {
            return true;
        }
        // API 文档不拦截
        if (request.getRequestURI().contains("doc.html") ||
                request.getRequestURI().contains("swagger") ||
                request.getRequestURI().contains("favicon.ico") ||
                request.getRequestURI().contains("webjars") ||
                request.getRequestURI().contains("api-docs")
        ) {
            return true;
        }
        String token = request.getHeader(ADMIN_COOKIE_NAME_TOKEN);
        // 图片资源请求不会携带token。需要从cookie中获取
        if (StrUtil.isBlank(token)) {
            token = ServletUtil.getTokenByCookie4Admin();
        }
        if (StrUtil.isBlank(token)) {
            throw new BusinessException(ResponseCodeEnum.CODE_901.getCode(), "登录状态已失效，请重新登录");
        }
        String account = redisComponent.getAdminToken(token);
        if (StrUtil.isBlank(account)) {
            throw new BusinessException(ResponseCodeEnum.CODE_901.getCode(), "登录状态已失效，请重新登录");
        }

        return true;
    }
}
