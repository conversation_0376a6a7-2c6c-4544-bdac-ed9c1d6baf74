package com.bilibili.common.interceptor;

import com.bilibili.common.constants.CookieConstant;
import com.bilibili.common.constants.RedisConstant;
import com.bilibili.common.constants.TimeConstant;
import com.bilibili.common.model.vo.UserVO;
import com.bilibili.common.utils.RedisUtil;
import com.bilibili.common.utils.ServletUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.UUID;

/**
 * 刷新token拦截器
 */
@Slf4j
@Component
public class RefreshTokenInterceptor implements HandlerInterceptor {

    @Resource
    private RedisUtil redisUtil;



    // TODO 该方法还需考虑并发情况
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        log.info("RefreshTokenInterceptor preHandle executed");
        String token = request.getHeader(CookieConstant.WEB_COOKIE_NAME_TOKEN);
        if (token == null) {
            return true;
        }
        UserVO userVO = (UserVO) redisUtil.get(RedisConstant.REDIS_WEB_TOKEN_PREFIX + token);
        // token有效期不足一天时刷新token
        if (userVO != null && userVO.getExpireAt() - System.currentTimeMillis() < TimeConstant.ONE_DAY_MILLIS) {
            // 更新token
            String newToken = UUID.randomUUID().toString();
            // 设置新token到cookie中
            ServletUtil.saveToken2Cookie4Web(newToken);
            // 设置新token有效期
            userVO.setExpireAt(System.currentTimeMillis() + TimeConstant.ONE_DAY_MILLIS);
            // TODO 设置粉丝数、关注数、当前硬币数
            // 存储新token到redis中
            redisUtil.setex(RedisConstant.REDIS_WEB_TOKEN_PREFIX + newToken, userVO, TimeConstant.ONE_DAY_MILLIS * 7L);
            // 更新旧token有效期，确保请求完成
            redisUtil.setex(RedisConstant.REDIS_WEB_TOKEN_PREFIX + token, userVO, TimeConstant.TEN_SECOND_MILLIS);
        }
        return true;
    }


}
