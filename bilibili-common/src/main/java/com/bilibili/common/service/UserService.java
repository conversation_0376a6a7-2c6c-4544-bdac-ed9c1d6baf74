package com.bilibili.common.service;

import com.bilibili.common.model.vo.UserVO;

import java.util.Map;


/**
 * 用户信息表 业务接口
 */
public interface UserService {

	/**
	 * 生成图形验证码
	 * @return
	 */
    Map<String, String> generateCaptcha();


	Boolean register(String email, String nickName, String registerPassword, String reRegisterPassword, String checkCode, String checkCodeKey);


	/**
	 * 登录
	 * @param email 邮箱
	 * @param password 密码
	 * @param checkCode 验证码
	 * @param checkCodeKey 验证码key
	 * @return
	 */
	UserVO login(String email, String password, String checkCode, String checkCodeKey);

	/**
	 * 退出登录
	 */
	void logout();
}