package com.bilibili.common.service;

import com.bilibili.common.model.entity.Category;
import com.bilibili.common.model.vo.LoadCategoryResponse;

import java.util.List;

public interface CategoryService {

    /**
     * 加载分类列表
     * @return 分类列表
     */
    List<LoadCategoryResponse> loadCategory();


    /**
     * 新增/更新分类
     * @param category 分类
     */
    void saveOrUpdateCategory(Category category);


    /**
     * 删除分类
     * @param categoryId 分类ID
     */
    void deleteCategory(Integer categoryId);

    void changeSort(String categoryIds);
}
