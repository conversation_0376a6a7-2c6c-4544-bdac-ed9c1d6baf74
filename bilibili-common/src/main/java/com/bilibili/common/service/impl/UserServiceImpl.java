package com.bilibili.common.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.SecureUtil;
import com.bilibili.common.component.RedisComponent;
import com.bilibili.common.constants.IntegerConstant;
import com.bilibili.common.constants.RedisConstant;
import com.bilibili.common.constants.TimeConstant;
import com.bilibili.common.exception.BusinessException;
import com.bilibili.common.mapper.UserMapper;
import com.bilibili.common.model.entity.User;
import com.bilibili.common.model.dto.UserInfoQuery;
import com.bilibili.common.model.enums.ResponseCodeEnum;
import com.bilibili.common.model.enums.UserSexEnum;
import com.bilibili.common.model.enums.UserStatusEnum;
import com.bilibili.common.model.vo.UserVO;
import com.bilibili.common.service.UserService;
import com.bilibili.common.utils.RedisUtil;
import com.bilibili.common.utils.ServletUtil;
import com.wf.captcha.ArithmeticCaptcha;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import java.util.*;

/**
 * 用户信息表 业务接口实现
 */
@Service
public class UserServiceImpl implements UserService {

    @Resource
    private UserMapper<User, UserInfoQuery> userMapper;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private RedisComponent redisComponent;


    /**
     * 生成图形验证码
     *
     * @return
     */
    @Override
    public Map<String, String> generateCaptcha() {
        ArithmeticCaptcha captcha = new ArithmeticCaptcha(100, 42);
        // 获取图形验证码的计算结果
        String checkCode = captcha.text();
        // 获取图形
        String captchaBase64 = captcha.toBase64();
        // 生成用于标识会话的key
        String checkCodeKey = UUID.randomUUID().toString();
        // 存入redis并设置过期时间
        redisComponent.saveCheckCode(checkCodeKey, checkCode);
        // 封装返回结果
        HashMap<String, String> result = new HashMap<>();
        result.put("checkCodeKey", checkCodeKey);
        result.put("checkCode", captchaBase64);
        return result;
    }


    @Override
    public UserVO login(String email, String password, String checkCode, String checkCodeKey) {
        // 1. 清除redis中的旧token
        String oldToken = ServletUtil.getTokenByCookie4Web();
        if (oldToken != null) {
            redisUtil.delete(RedisConstant.REDIS_WEB_TOKEN_PREFIX + oldToken);
        }
        String checkCodeRedisKey = RedisConstant.REDIS_CAPTCHA_PREFIX + checkCodeKey;
        User user = null;
        // 2. 校验登录参数
        try {
            if (!redisUtil.keyExists(checkCodeRedisKey)) {
                throw new BusinessException(ResponseCodeEnum.CODE_601.getCode(), "验证码已过期");
            }
            String checkCodeInRedis = (String) redisUtil.get(checkCodeRedisKey);
            if (!checkCodeInRedis.equalsIgnoreCase(checkCode)) {
                throw new BusinessException(ResponseCodeEnum.CODE_601.getCode(), "验证码不正确");
            }
            user = this.userMapper.selectByEmail(email);
            if (user == null || !user.getPassword().equals(password)) {
                throw new BusinessException(ResponseCodeEnum.CODE_601.getCode(), "邮箱或密码不正确");
            }
            if (UserStatusEnum.DISABLE.getCode().equals(user.getStatus())) {
                throw new BusinessException(ResponseCodeEnum.CODE_601.getCode(), "用户已被禁用");
            }
        } finally {
            // 删除缓存在redis的验证码
            redisUtil.delete(checkCodeRedisKey);
        }

        // 3. 更新IP和登录时间
        user.setLastLoginIp(ServletUtil.getClientIP());
        user.setLastLoginTime(new Date());
        this.userMapper.updateByUserId(user, user.getUserId());

        // 4. 封装返回对象，并将用户信息存入redis
        UserVO userVO = BeanUtil.copyProperties(user, UserVO.class);
        String token = UUID.randomUUID().toString();
        userVO.setToken(token);
        userVO.setExpireAt(System.currentTimeMillis() + TimeConstant.ONE_DAY_MILLIS * 7L);
        // TODO 设置粉丝数、关注数、当前硬币数
        redisComponent.saveTokenInfo4Web(token, userVO);
        // 5. 后端手动将token设置到Cookie
        ServletUtil.saveToken2Cookie4Web(token);

        return userVO;
    }

    /**
     * 退出登录
     */
    @Override
    public void logout() {
        // 1. 必须先清除redis
        String token = ServletUtil.getTokenByCookie4Web();
        if (token != null) {
            redisUtil.delete(RedisConstant.REDIS_WEB_TOKEN_PREFIX + token);
        }
        // 2. 再清除cookie
        ServletUtil.cleanCookie4Web();

    }

    /**
     * 注册
     *
     * @param email
     * @param nickName
     * @param registerPassword
     * @param reRegisterPassword
     * @param checkCode
     * @param checkCodeKey
     * @return
     */
    @Override
    public Boolean register(String email, String nickName, String registerPassword, String reRegisterPassword, String checkCode, String checkCodeKey) {
        // 1. 校验参数
        String checkCodeRedisKey = RedisConstant.REDIS_CAPTCHA_PREFIX + checkCodeKey;
        User user = this.userMapper.selectByEmail(email);
        try {
            if (user != null) {
                throw new BusinessException(ResponseCodeEnum.CODE_601.getCode(), "邮箱已经存在");
            }
            user = this.userMapper.selectByNickName(nickName);
            if (user != null) {
                throw new BusinessException(ResponseCodeEnum.CODE_601.getCode(), "昵称已经存在");
            }
            if (!registerPassword.equals(reRegisterPassword)) {
                throw new BusinessException(ResponseCodeEnum.CODE_601.getCode(), "两次输入的密码不一致");
            }
            if (!redisUtil.keyExists(checkCodeRedisKey)) {
                throw new BusinessException(ResponseCodeEnum.CODE_601.getCode(), "验证码已过期");
            }
            String checkCodeInRedis = (String) redisUtil.get(checkCodeRedisKey);
            if (!checkCodeInRedis.equalsIgnoreCase(checkCode)) {
                throw new BusinessException(ResponseCodeEnum.CODE_601.getCode(), "验证码不正确");
            }
        } finally {
            redisUtil.delete(checkCodeRedisKey);
        }
        // 2. 插入数据库
        user = new User();
        user.setUserId(RandomUtil.randomNumbers(10));
        user.setNickName(nickName);
        user.setEmail(email);
        user.setPassword(SecureUtil.md5(registerPassword));
        user.setSex(UserSexEnum.UNKNOWN.getCode());
        user.setTheme(IntegerConstant.ONE);
        user.setJoinTime(new Date());
        user.setStatus(UserStatusEnum.NORMAL.getCode());
        // TODO 设置硬币
        user.setTotalCoinCount(IntegerConstant.ZERO);
        user.setCurrentCoinCount(IntegerConstant.ZERO);
        user.setCreateTime(new Date());
        user.setUpdateTime(new Date());
        user.setIsDeleted(IntegerConstant.ZERO);
        this.userMapper.insert(user);

        return Boolean.TRUE;
    }


}