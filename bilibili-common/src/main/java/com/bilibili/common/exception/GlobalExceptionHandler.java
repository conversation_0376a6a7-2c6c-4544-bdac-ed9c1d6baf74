package com.bilibili.common.exception;

import com.bilibili.common.model.enums.ResponseCodeEnum;
import com.bilibili.common.utils.Response;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.NoHandlerFoundException;
import org.springframework.web.servlet.resource.NoResourceFoundException;

import java.util.Optional;

@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {


//    @ExceptionHandler({NoHandlerFoundException.class, NoResourceFoundException.class})
//    @ResponseBody
//    public Response<Object> handleNoHandlerFoundException(HttpServletRequest request, Exception e) {
//        log.warn("请求地址不存在，请求路径: {}, 错误码: {}, 错误信息: {}", request.getRequestURI(), ResponseCodeEnum.CODE_404.getCode(), ResponseCodeEnum.CODE_404.getMsg());
//        return Response.fail(ResponseCodeEnum.CODE_404.getCode(), ResponseCodeEnum.CODE_404.getMsg());
//    }

    @ExceptionHandler({BindException.class, MethodArgumentTypeMismatchException.class})
    @ResponseBody
    public Response<Object> handleValidationException(HttpServletRequest request, Exception e) {
        log.warn("请求参数错误，请求路径: {}, 错误码: {}, 错误信息: {}", request.getRequestURI(), ResponseCodeEnum.CODE_600.getCode(), ResponseCodeEnum.CODE_600.getMsg());
        return Response.fail(ResponseCodeEnum.CODE_600.getCode(), ResponseCodeEnum.CODE_600.getMsg());
    }

    /**
     * 捕获自定义业务异常
     * @return
     */
    @ExceptionHandler({ BusinessException.class })
    @ResponseBody
    public Response<Object> handleBusinessException(HttpServletRequest request, BusinessException e) {
        log.warn("业务异常，请求路径: {}, 错误码: {}, 错误信息: {}", request.getRequestURI(), e.getCode(), e.getMessage());
        return Response.fail(e);
    }


    @ExceptionHandler({DuplicateKeyException.class})
    @ResponseBody
    public Response<Object> handleDuplicateKeyException(HttpServletRequest request, DuplicateKeyException e) {
        log.warn("数据重复异常，请求路径: {}, 错误码: {}, 错误信息: {}", request.getRequestURI(), ResponseCodeEnum.CODE_601.getCode(), ResponseCodeEnum.CODE_601.getMsg());
        return Response.fail(ResponseCodeEnum.CODE_601.getCode(), ResponseCodeEnum.CODE_601.getMsg());
    }


    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    public Response<Object> handleException(Exception e, HttpServletRequest request) {
        log.error("系统异常，请求地址: {}, 错误信息:", request.getRequestURL(), e);
        return Response.fail(ResponseCodeEnum.CODE_500.getCode(), ResponseCodeEnum.CODE_500.getMsg());
    }

    /**
     * 捕获 guava 参数校验异常
     * @return
     */
    @ExceptionHandler({ IllegalArgumentException.class })
    @ResponseBody
    public Response<Object> handleIllegalArgumentException(HttpServletRequest request, IllegalArgumentException e) {
        // 参数错误异常码
        Integer errorCode = ResponseCodeEnum.CODE_600.getCode();
        // 错误信息
        String errorMessage = e.getMessage();

        log.warn("参数错误，请求路径: {}, 错误码: {}, 错误信息: {}", request.getRequestURI(), errorCode, errorMessage);

        return Response.fail(errorCode, errorMessage);
    }

    /**
     * 捕获参数校验异常
     * @return
     */
    @ExceptionHandler({ MethodArgumentNotValidException.class })
    @ResponseBody
    public Response<Object> handleMethodArgumentNotValidException(HttpServletRequest request, MethodArgumentNotValidException e) {
        // 参数错误异常码
        Integer errorCode = ResponseCodeEnum.CODE_600.getCode();

        // 获取 BindingResult
        BindingResult bindingResult = e.getBindingResult();

        StringBuilder sb = new StringBuilder();

        // 获取校验不通过的字段，并组合错误信息，格式为： email 邮箱格式不正确, 当前值: '123124qq.com';
        Optional.ofNullable(bindingResult.getFieldErrors()).ifPresent(errors -> {
            errors.forEach(error ->
                    sb.append(error.getField())
                            .append(" ")
                            .append(error.getDefaultMessage())
                            .append(", 当前值: '")
                            .append(error.getRejectedValue())
                            .append("'; ")

            );
        });

        // 错误信息
        String errorMessage = sb.toString();

        log.warn("{} request error, errorCode: {}, errorMessage: {}", request.getRequestURI(), errorCode, errorMessage);

        return Response.fail(errorCode, errorMessage);
    }

}
