<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.bilibili.common.mapper.CategoryMapper">

    <select id="selectCategoryList" resultType="com.bilibili.common.model.vo.LoadCategoryResponse">
        SELECT * FROM category order by sort
    </select>
    <select id="selectCategoryByCategoryCode" resultType="com.bilibili.common.model.entity.Category">
        SELECT * FROM category WHERE category_code = #{categoryCode}
    </select>
    <select id="selectMaxSort" resultType="java.lang.Integer">
        SELECT IFNULL(MAX(sort), 0) FROM category WHERE category_pid = #{categoryPid}
    </select>

    <insert id="insert">
        insert into category(category_code, category_name, category_pid, icon, background, sort)
        values(#{category.categoryCode}, #{category.categoryName}, #{category.categoryPid}, #{category.icon}, #{category.background}, #{category.sort})
    </insert>

    <update id="updateByCategoryId">
        UPDATE category
        <set>
            <if test="category.categoryName != null">
                category_name = #{category.categoryName},
            </if>
            <if test="category.categoryCode != null">
                category_code = #{category.categoryCode},
            </if>
            <if test="category.categoryPid != null">
                category_pid = #{category.categoryPid},
            </if>
            <if test="category.icon != null">
                icon = #{category.icon},
            </if>
            <if test="category.background != null">
                background = #{category.background},
            </if>
            <if test="category.sort != null">
                sort = #{category.sort},
            </if>
        </set>
        where category_id = #{categoryId}
    </update>

    <delete id="deleteByCategoryId">
        DELETE FROM category WHERE category_id = #{categoryId} OR category_pid = #{categoryId}
    </delete>

    <update id="batchUpdateCategorySort">
        <foreach collection="updateCategoryList" item="c" separator=";">
            UPDATE category
            <set>
                <if test="c.sort != null">
                    sort = #{c.sort},
                </if>
            </set>
            where category_id = #{c.categoryId}
        </foreach>
    </update>

</mapper>