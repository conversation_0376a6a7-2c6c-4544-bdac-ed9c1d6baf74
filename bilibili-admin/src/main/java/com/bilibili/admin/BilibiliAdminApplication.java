package com.bilibili.admin;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication(scanBasePackages = "com.bilibili")
@MapperScan(basePackages = "com.bilibili.common.mapper") // 批量扫描 mapper 接口，接口无需再添加 @Mapper注解
public class BilibiliAdminApplication {

    public static void main(String[] args) {
        SpringApplication.run(BilibiliAdminApplication.class, args);
    }

}
