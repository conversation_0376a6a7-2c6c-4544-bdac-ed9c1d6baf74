package com.bilibili.admin.controller;


import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.bilibili.common.component.RedisComponent;
import com.bilibili.common.config.app.AppConfig;
import com.bilibili.common.exception.BusinessException;
import com.bilibili.common.model.dto.admin.account.LoginRequest;
import com.bilibili.common.service.UserService;
import com.bilibili.common.utils.Response;
import com.bilibili.common.utils.ServletUtil;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;


/**
 * 用户信息表 Controller
 */
@RestController
@RequestMapping("/account")
public class AccountController {

	@Resource
	private UserService userService;
	@Resource
	private AppConfig appConfig;
	@Resource
	private RedisComponent redisComponent;

	/**
     * 获取图形验证码
	 * @return
	 */
	@RequestMapping("/checkCode")
	public Response<Map<String, String>> checkCode() {
		return Response.success(userService.generateCaptcha());
	}


	/**
	 * 登录
	 * @param loginRequest
	 * @return
	 */
	@RequestMapping("/login")
	public Response<String> login(@RequestBody @Validated LoginRequest loginRequest) {
		// 尝试获取旧Token
		String oldToken = ServletUtil.getTokenByCookie4Admin();
		if (StrUtil.isNotBlank(oldToken)) {
			redisComponent.deleteToken4Admin(oldToken);
		}
		String account = loginRequest.getAccount();
		String password = loginRequest.getPassword();
		String checkCode = loginRequest.getCheckCode();
		String checkCodeKey = loginRequest.getCheckCodeKey();
		String adminAccount = appConfig.getAdminAccount();
		String adminPassword = appConfig.getAdminPassword();
		String checkCodeInRedis = redisComponent.getCheckCode(checkCodeKey);
		try {
			if (!checkCode.equals(checkCodeInRedis)) {
				throw new BusinessException("验证码不正确");
			}
			if (!account.equals(adminAccount) || !password.equals(SecureUtil.md5(adminPassword))) {
				throw new BusinessException("账号或密码不正确");
			}
		} finally {
			redisComponent.deleteCheckCode(checkCodeKey);

		}
		String token = redisComponent.saveTokenInfo4Admin(account);
		// 后端手动将token设置到Cookie
		ServletUtil.saveToken2Cookie4Admin(token);
		return Response.success(account);
	}

	/**
	 * 退出登录
	 * @return
	 */
	@RequestMapping("/logout")
	public Response<Void> logout() {
		userService.logout();
		return Response.success();
	}

}