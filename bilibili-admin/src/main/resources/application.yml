server:
  port: 7070
  servlet:
    context-path: /admin
spring:
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 15MB
  application:
    name: bilibili-admin
  datasource:
    url: *********************************************************************************************************************************************************
    username: root
    password: ly@LYmysql
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    druid: # Druid 连接池
      initial-size: 5 # 初始化连接池大小
      min-idle: 5 # 最小连接池数量
      max-active: 20 # 最大连接池数量
      max-wait: 60000 # 连接时最大等待时间（单位：毫秒）
      test-while-idle: true
      time-between-eviction-runs-millis: 60000 # 配置多久进行一次检测，检测需要关闭的连接（单位：毫秒）
      min-evictable-idle-time-millis: 300000 # 配置一个连接在连接池中最小生存的时间（单位：毫秒）
      max-evictable-idle-time-millis: 900000 # 配置一个连接在连接池中最大生存的时间（单位：毫秒）
      validation-query: SELECT 1 FROM DUAL # 配置测试连接是否可用的查询 sql
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: false
      web-stat-filter:
        enabled: true
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/* # 配置监控后台访问路径
        login-username: admin # 配置监控后台登录的用户名、密码
        login-password: admin
      filter:
        stat:
          enabled: true
          log-slow-sql: true # 开启慢 sql 记录
          slow-sql-millis: 2000 # 若执行耗时大于 2s，则视为慢 sql
          merge-sql: true
        wall: # 防火墙
          config:
            multi-statement-allow: true
  data:
    redis:
      database: 0
      host: ************
      port: 6379
      password: ly@LYredis
      jedis:
        pool:
          max-active: 20
          max-wait: -1
          max-idle: 10
          min-idle: 0
      timeout: 2000

# mybatis 配置
mybatis:
  mapper-locations: classpath:mapper/*.xml # 指定扫描 resources/mapper/ 下的所有 XML
  configuration:
    map-underscore-to-camel-case: true # mybatis 大小写转驼峰
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl # 控制台输出完整 SQL

# logback-spring.xml 配置项
project:
  folder: D:/Workspace/java_code/bilibili-backend/
log:
  root:
    level: info
# 后台管理
admin:
  account: admin
  password: admin123

