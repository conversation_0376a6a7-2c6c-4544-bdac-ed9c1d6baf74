2025-07-30 22:37:21 [DEBUG][org.springframework.boot.availability.ApplicationAvailabilityBean][onApplicationEvent][77]-> Application availability state ReadinessState changed from ACCEPTING_TRAFFIC to REFUSING_TRAFFIC
2025-07-30 22:37:21 [DEBUG][org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext][doClose][1158]-> Closing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@67ec8477, started on Wed Jul 30 22:23:19 CST 2025
2025-07-30 22:37:21 [DEBUG][org.springframework.context.support.DefaultLifecycleProcessor][stop][519]-> Stopping beans in phase 2147483647
2025-07-30 22:37:21 [DEBUG][org.springframework.context.support.DefaultLifecycleProcessor][lambda$doStop$4][388]-> Bean 'container' completed its stop procedure
2025-07-30 22:37:21 [DEBUG][org.springframework.context.support.DefaultLifecycleProcessor][lambda$doStop$4][388]-> Bean 'redisKeyValueAdapter' completed its stop procedure
2025-07-30 22:37:21 [DEBUG][org.springframework.context.support.DefaultLifecycleProcessor][stop][519]-> Stopping beans in phase 2147482623
2025-07-30 22:37:21 [INFO][org.springframework.boot.web.embedded.tomcat.GracefulShutdown][shutDownGracefully][54]-> Commencing graceful shutdown. Waiting for active requests to complete
2025-07-30 22:37:21 [INFO][org.springframework.boot.web.embedded.tomcat.GracefulShutdown][doShutdown][76]-> Graceful shutdown complete
2025-07-30 22:37:21 [DEBUG][org.springframework.context.support.DefaultLifecycleProcessor][lambda$doStop$4][388]-> Bean 'webServerGracefulShutdown' completed its stop procedure
2025-07-30 22:37:21 [DEBUG][org.springframework.context.support.DefaultLifecycleProcessor][stop][519]-> Stopping beans in phase 2147481599
2025-07-30 22:37:21 [DEBUG][org.springframework.context.support.DefaultLifecycleProcessor][lambda$doStop$4][388]-> Bean 'webServerStartStop' completed its stop procedure
2025-07-30 22:37:21 [DEBUG][org.springframework.context.support.DefaultLifecycleProcessor][stop][519]-> Stopping beans in phase 1073741823
2025-07-30 22:37:21 [DEBUG][org.springframework.context.support.DefaultLifecycleProcessor][lambda$doStop$4][388]-> Bean 'applicationTaskExecutor' completed its stop procedure
2025-07-30 22:37:21 [DEBUG][org.springframework.context.support.DefaultLifecycleProcessor][stop][519]-> Stopping beans in phase 0
2025-07-30 22:37:21 [DEBUG][org.springframework.context.support.DefaultLifecycleProcessor][lambda$doStop$4][388]-> Bean 'redisConnectionFactory' completed its stop procedure
2025-07-30 22:37:21 [DEBUG][org.springframework.context.support.DefaultLifecycleProcessor][stop][519]-> Stopping beans in phase -2147483647
2025-07-30 22:37:21 [DEBUG][org.springframework.context.support.DefaultLifecycleProcessor][lambda$doStop$4][388]-> Bean 'springBootLoggingLifecycle' completed its stop procedure
2025-07-30 22:37:21 [DEBUG][org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager][shutdown][232]-> Connection manager is shutting down
2025-07-30 22:37:21 [DEBUG][org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager][shutdown][234]-> Connection manager shut down
2025-07-30 22:37:21 [DEBUG][org.springframework.beans.factory.support.DisposableBeanAdapter][invokeCustomDestroyMethod][329]-> Unknown return value type from custom destroy method 'shutdown' on bean with name 'elasticsearchClient': class co.elastic.clients.elasticsearch.shutdown.ElasticsearchShutdownClient
2025-07-30 22:37:21 [DEBUG][org.springframework.jmx.export.annotation.AnnotationMBeanExporter][destroy][467]-> Unregistering JMX-exposed beans on shutdown
2025-07-30 22:37:21 [DEBUG][org.springframework.jmx.export.annotation.AnnotationMBeanExporter][unregisterBeans][186]-> Unregistering JMX-exposed beans
2025-07-30 22:37:21 [DEBUG][org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor][shutdown][370]-> Shutting down ExecutorService 'applicationTaskExecutor'
2025-07-30 22:37:21 [DEBUG][org.springframework.beans.factory.support.DisposableBeanAdapter][logDestroyMethodCompletion][361]-> Custom destroy method 'shutdown' on bean with name 'lettuceClientResources' completed asynchronously
2025-07-30 22:37:21 [INFO][com.alibaba.druid.pool.DruidDataSource][close][2212]-> {dataSource-1} closing ...
2025-07-30 22:37:21 [INFO][com.alibaba.druid.pool.DruidDataSource][close][2285]-> {dataSource-1} closed
